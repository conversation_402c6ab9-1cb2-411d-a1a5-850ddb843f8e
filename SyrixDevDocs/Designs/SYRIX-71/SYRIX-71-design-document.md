# SYRIX-71 Design Documentation: SharePoint Malware Protection

**Version:** 1.0  
**Date:** 2025-08-31  
**Policy ID:** MS.SHAREPOINT.5.1v1  
**CIS Benchmark:** 7.3.1 - Prevent Download of Infected Files  

## Overview

This document provides comprehensive technical design for implementing SharePoint malware protection security remediation in the Syrix platform. The solution prevents download of infected files through automated policy enforcement and compliance validation.

## Requirements Analysis

### Functional Requirements
- **Primary Goal:** Implement automated detection and remediation for SharePoint malware protection settings
- **Security Objective:** Ensure infected files are blocked from download in SharePoint Online
- **Compliance Target:** Meet CIS MS365 Benchmark 7.3.1 requirements
- **Integration:** Seamlessly integrate with existing Syrix SharePoint infrastructure

### Technical Requirements
- **Policy Framework:** @PolicyRemediator("MS.SHAREPOINT.5.1v1") annotation system
- **PowerShell Integration:** SharePoint Online Management Shell commands
- **Async Processing:** CompletableFuture-based execution pattern
- **Configuration Management:** Extension of existing SharepointRemediationConfig
- **Audit Logging:** Complete remediation trail with rollback capability

## Technical Architecture

### 1. System Integration Points

```
┌─────────────────┐    ┌──────────────────────┐    ┌─────────────────────┐
│   Syrix Core    │    │  SharePoint Module   │    │  Microsoft Graph    │
│                 │    │                      │    │  & PowerShell       │
│  - OPA Engine   │◄──►│ - Remediator         │◄──►│                     │
│  - Audit Log    │    │ - Config Service     │    │ - Get-PnPTenant     │
│  - Storage      │    │ - Policy Validation  │    │ - Malware Settings  │
└─────────────────┘    └──────────────────────┘    └─────────────────────┘
```

### 2. Data Flow Architecture

```
JIRA Task → Policy Detection → Configuration Retrieval → Validation → Remediation → Audit
    │             │                     │                  │             │          │
    │             │                     │                  │             │          │
    ▼             ▼                     ▼                  ▼             ▼          ▼
SYRIX-71    MS.SHAREPOINT.5.1v1   Get-PnPTenant    Rego Policy   PowerShell   Storage
                                  MalwareSettings    Evaluation    Commands     S3/Local
```

## 4-Component Security Bundle Design

### Component 1: SharePointMalwareProtectionRemediator Class

**Location:** `/io/syrix/products/microsoft/sharepoint/remediation/SharePointMalwareProtectionRemediator.java`

**Class Structure:**
```java
@PolicyRemediator("MS.SHAREPOINT.5.1v1")
public class SharePointMalwareProtectionRemediator extends SPRemediatorBase 
    implements IPolicyRemediatorRollback {
    
    private static final Logger log = LoggerFactory.getLogger(SharePointMalwareProtectionRemediator.class);
    private final MalwareProtectionConfig malwareConfig;
    
    // Constructor for remediation
    public SharePointMalwareProtectionRemediator(
        PowerShellSharepointClient client, 
        SharePointTenantProperties tenant, 
        SharepointRemediationConfig spConfig
    )
    
    // Constructor for rollback
    public SharePointMalwareProtectionRemediator(
        PowerShellSharepointClient client, 
        SharePointTenantProperties tenant
    )
}
```

**Key Methods:**
- `remediate_()`: Validates and configures malware protection settings
- `rollback()`: Restores previous malware protection configuration
- `runCommand()`: Executes PowerShell commands with retry logic
- `checkMalwareProtectionStatus()`: Validates current malware settings

### Component 2: SharePointConfigurationService Extension

**Location:** `/io/syrix/products/microsoft/sharepoint/SharePointConfigurationService.java`

**Method Addition:**
```java
private CompletableFuture<JsonNode> getMalwareProtection() {
    SPShellCommand<MalwareProtectionProperties> command = 
        SPShellCommand.PnPTenant.GET_MALWARE_PROTECTION();
    
    return Retry.executeWithRetry(() -> powerShellSharepointClient.execute(command), MAX_RETRY)
        .thenCompose(this::processMalwareProtectionResult)
        .exceptionallyCompose(this::handleMalwareProtectionError);
}
```

**Integration Point:**
- Extends `exportConfiguration()` method with malware protection data
- Adds "SharePoint_malware_protection" key to configuration map
- Follows existing async pattern with CompletableFuture

### Component 3: SharepointConfig.rego Policy Extension

**Location:** `/src/main/resources/rego/SharepointConfig.rego`

**New Section Addition:**
```rego
###################
# MS.SHAREPOINT.5 #
###################

#
# MS.SHAREPOINT.5.1v1
#--

# Malware protection should be enabled for SharePoint Online
# to prevent download of infected files per CIS MS365 7.3.1
tests contains {
    "PolicyId": "MS.SHAREPOINT.5.1v1",
    "Criticality": "Shall",
    "Commandlet": ["Get-PnPTenant"],
    "ActualValue": [Tenant.MalwareProtectionEnabled],
    "ReportDetails": ReportDetailsBoolean(Status),
    "RequirementMet": Status
} if {
    Status := Tenant.MalwareProtectionEnabled == true
}
#--
```

**Policy Logic:**
- Validates `MalwareProtectionEnabled` tenant property
- Uses standard ReportDetailsBoolean pattern
- Follows existing policy structure and naming conventions

### Component 4: sharepoint.md Baseline Documentation

**Location:** `/SyrixCommon/SyrixBaselinesUtils/src/main/resources/baselines/sharepoint.md`

**Section 5 Addition:**
```markdown
## 5. Malware Protection and File Security

### MS.SHAREPOINT.5.1v1
Ensure malware protection is enabled for SharePoint Online to prevent download of infected files.

**Rationale:**
Microsoft Defender for Office 365 provides malware protection for SharePoint Online by scanning files for malicious content and preventing download of infected files.

**Configuration:**
SharePoint Online tenant must have malware protection enabled through Safe Attachments for SharePoint feature.

**Audit:**
- Navigate to Microsoft 365 Admin Center
- Go to Security & Compliance > Threat Management > Safe Attachments
- Verify "Enable Safe Attachments for SharePoint and OneDrive" is enabled

**Remediation:**
Enable Safe Attachments for SharePoint through PowerShell:
```powershell
Get-PnPTenant | Select MalwareProtectionEnabled
Set-PnPTenant -MalwareProtectionEnabled $true
```

**CIS Reference:** 7.3.1
```

## Data Model Extensions

### SharepointRemediationConfig Enhancement

**New Properties:**
```java
public class SharepointRemediationConfig {
    // Existing properties
    public SharingCapabilityEntity sharingCapability;
    public SharingCapabilityEntity odbSharingCapability;
    public List<String> allowedDomains;
    
    // Malware protection property - CIS 7.3.1
    public boolean disallowInfectedFileDownload;
}
```

### SharePointTenantProperties Extension

**New Malware Fields:**
```java
public class SharePointTenantProperties {
    // Existing fields...
    
    // New malware protection fields
    public boolean malwareProtectionEnabled;
    public String malwareProtectionLevel;
    public boolean malwareQuarantineEnabled;
    public String malwareNotificationEmail;
}
```

## PowerShell Integration Design

### Command Structure
```java
public class SPShellCommand {
    public static class PnPTenant {
        public static SPShellCommand<MalwareProtectionProperties> GET_MALWARE_PROTECTION() {
            return new SPShellCommand<>(
                "Get-PnPTenant",
                Arrays.asList("-MalwareProtectionEnabled", "-MalwareQuarantineEnabled"),
                MalwareProtectionProperties.class
            );
        }
        
        public static SPShellCommand<GeneralResult> SET_MALWARE_PROTECTION(
            String objectIdentity, 
            boolean enabled,
            boolean prevEnabled
        ) {
            return new SPShellCommand<>(
                "Set-PnPTenant",
                Arrays.asList(
                    new Parameter("MalwareProtectionEnabled", enabled, prevEnabled)
                ),
                GeneralResult.class
            );
        }
    }
}
```

### Response Handling
```java
public class MalwareProtectionProperties {
    public boolean malwareProtectionEnabled;
    public boolean malwareQuarantineEnabled;
    public String malwareNotificationEmail;
    public ErrorInfo errorInfo;
}
```

## Security and Compliance Considerations

### CIS MS365 Benchmark 7.3.1 Compliance
- **Requirement:** Prevent download of infected files in SharePoint Online
- **Implementation:** Enable Safe Attachments for SharePoint and OneDrive
- **Validation:** Verify malware protection is active and configured properly
- **Monitoring:** Track remediation actions and compliance status

### Security Implementation Standards
- **No Hardcoded Credentials:** All authentication through existing MSAL integration
- **Audit Trail:** Complete logging of all remediation actions
- **Error Handling:** Graceful degradation with proper error reporting
- **Rollback Support:** Ability to restore previous configuration
- **Encryption:** All stored configuration data encrypted at rest

### Compliance Validation
- **Policy Evaluation:** OPA/Rego rules validate configuration compliance
- **Real-time Monitoring:** Continuous compliance status tracking
- **Remediation Tracking:** Historical record of all security changes
- **Reporting:** Compliance status reporting for auditing purposes

## Implementation Strategy

### Phase 1: Foundation
1. Extend SharepointRemediationConfig with malware protection fields
2. Add MalwareProtectionProperties model class
3. Create SPShellCommand for malware protection operations

### Phase 2: Core Implementation
1. Implement SharePointMalwareProtectionRemediator class
2. Extend SharePointConfigurationService with getMalwareProtection()
3. Add PowerShell command integration and response handling

### Phase 3: Policy Integration
1. Add MS.SHAREPOINT.5.1v1 policy to SharepointConfig.rego
2. Update sharepoint.md baseline with Section 5
3. Integrate with existing OPA policy evaluation engine

### Phase 4: Testing & Validation
1. Unit tests for remediator class and configuration service
2. Integration tests with PowerShell SharePoint client
3. Policy validation tests with OPA engine
4. End-to-end security remediation testing

## Integration Points

### Existing Infrastructure Reuse (85%)
- **SPRemediatorBase:** PowerShell integration foundation
- **SharePointConfigurationService:** Configuration retrieval framework
- **PowerShellSharepointClient:** SharePoint API communication
- **SharePointTenantProperties:** Tenant data model
- **OPA Engine:** Policy evaluation and compliance validation

### New Components (15%)
- **MalwareProtectionProperties:** Malware-specific data model
- **Malware Protection Commands:** PowerShell command definitions
- **Section 5 Policies:** New malware protection policy rules
- **Baseline Documentation:** Section 5 malware protection guidelines

## Quality Assurance

### Code Quality Standards
- **Pattern Consistency:** Follow established Syrix development patterns
- **Error Handling:** Comprehensive exception handling and logging
- **Async Design:** CompletableFuture-based async operations
- **Testing Coverage:** Minimum 85% test coverage requirement
- **Documentation:** Comprehensive inline and technical documentation

### Security Standards
- **SAST Analysis:** Static Application Security Testing validation
- **Dependency Scanning:** Third-party library vulnerability assessment
- **Configuration Validation:** Secure configuration parameter handling
- **Audit Integration:** Complete integration with Syrix audit system

## Deployment Considerations

### Configuration Management
- **Environment Variables:** No hardcoded configuration values
- **Secret Management:** Integration with existing secret management system
- **Feature Flags:** Gradual rollout capability with feature toggles
- **Monitoring:** Integration with existing monitoring and alerting systems

### Operational Requirements
- **Rollback Planning:** Complete rollback capability for all changes
- **Monitoring Integration:** Metrics and alerting for malware protection status
- **Documentation Updates:** User and administrator documentation updates
- **Training Materials:** Security team training on new malware protection features

---

**Approval Required Before Code Generation**
This design document requires approval before proceeding to implementation phase. All technical specifications, security considerations, and integration points have been thoroughly analyzed and documented according to Syrix development standards.