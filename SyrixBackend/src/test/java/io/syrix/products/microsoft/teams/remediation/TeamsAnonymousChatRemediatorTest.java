package io.syrix.products.microsoft.teams.remediation;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.products.microsoft.base.ParameterChangeResult;
import io.syrix.products.microsoft.base.ParameterChangeStatus;
import io.syrix.products.microsoft.base.PolicyChangeResult;
import io.syrix.products.microsoft.base.RemediationResult;
import io.syrix.datamodel.task.remediation.teams.TeamsRemediationConfig;
import io.syrix.protocols.client.PowerShellTeamsClient;
import io.syrix.protocols.client.teams.powershell.command.CsTeamsCommand;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.concurrent.CompletableFuture;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class TeamsAnonymousChatRemediatorTest {

    @Mock
    private PowerShellTeamsClient mockClient;

    @Mock
    private TeamsRemediationConfig mockRemediationConfig;

    private ObjectMapper objectMapper;
    private ObjectNode configNode;
    private TeamsAnonymousChatRemediator remediator;

    @BeforeEach
    void setUp() {
        objectMapper = new ObjectMapper();
        configNode = objectMapper.createObjectNode();

        // Create test meeting policies JSON with proper casing to match UPPER_CAMEL_CASE naming strategy
        var meetingPoliciesArray = configNode.putArray("meeting_policies");

        meetingPoliciesArray.addObject()
            .put("Identity", "Global")
            .put("MeetingChatEnabledType", "Enabled");

        meetingPoliciesArray.addObject()
            .put("Identity", "TestPolicy")
            .put("MeetingChatEnabledType", "Enabled");

        // Note: Mock setup will be done in individual test methods

        remediator = new TeamsAnonymousChatRemediator(mockClient, configNode, mockRemediationConfig);
    }

    @Test
    void testGetPolicyId() {
        assertEquals("MS.TEAMS.1.8v1", remediator.getPolicyId());
    }

    @Test
    void testRemediateSuccess() throws Exception {
        // Mock successful PowerShell execution - return empty list of MeetingPolicy
        when(mockClient.execute(any()))
            .thenReturn(CompletableFuture.completedFuture(java.util.Collections.emptyList()));

        CompletableFuture<PolicyChangeResult> result = remediator.remediate_();
        PolicyChangeResult changeResult = result.get();

        assertEquals(RemediationResult.SUCCESS, changeResult.getResult());
        assertEquals("MS.TEAMS.1.8v1", changeResult.getPolicyId());
        assertEquals("All meeting policies secured - anonymous chat disabled successfully", changeResult.getDesc());

        // Verify that changes were tracked
        assertNotNull(changeResult.getChanges());
        assertEquals(2, changeResult.getChanges().size()); // Global and TestPolicy should be fixed

        // Verify PowerShell commands were executed for non-compliant policies
        verify(mockClient, times(2)).execute(any());
    }

    @Test
    void testRemediatePartialFailure() throws Exception {
        // Mock one success and one failure
        when(mockClient.execute(any()))
            .thenReturn(CompletableFuture.completedFuture(java.util.Collections.emptyList()))
            .thenReturn(CompletableFuture.failedFuture(new RuntimeException("PowerShell Error")));

        CompletableFuture<PolicyChangeResult> result = remediator.remediate_();
        PolicyChangeResult changeResult = result.get();

        assertEquals(RemediationResult.PARTIAL_SUCCESS, changeResult.getResult());
        assertEquals("MS.TEAMS.1.8v1", changeResult.getPolicyId());
        assertTrue(changeResult.getDesc().contains("Secured 1 policies, failed to secure 1 policies"));
        
        // Verify that changes were tracked for both attempts
        assertNotNull(changeResult.getChanges());
        assertEquals(2, changeResult.getChanges().size());
        
        verify(mockClient, times(2)).execute(any());
    }

    @Test
    void testRemediateAllCompliant() throws Exception {
        // Create config with only compliant policies
        ObjectNode compliantConfig = objectMapper.createObjectNode();
        var compliantPoliciesArray = compliantConfig.putArray("meeting_policies");

        compliantPoliciesArray.addObject()
            .put("Identity", "Global")
            .put("MeetingChatEnabledType", "EnabledExceptAnonymous");

        compliantPoliciesArray.addObject()
            .put("Identity", "Tag:SomePolicy")
            .put("MeetingChatEnabledType", "EnabledExceptAnonymous");

        TeamsAnonymousChatRemediator compliantRemediator = 
            new TeamsAnonymousChatRemediator(mockClient, compliantConfig, mockRemediationConfig);

        CompletableFuture<PolicyChangeResult> result = compliantRemediator.remediate_();
        PolicyChangeResult changeResult = result.get();

        assertEquals(RemediationResult.REQUIREMENT_MET, changeResult.getResult());
        assertEquals("MS.TEAMS.1.8v1", changeResult.getPolicyId());
        
        // No PowerShell commands should be executed
        verify(mockClient, never()).execute(any(CsTeamsCommand.class));
    }

    @Test
    void testRemediateNoPoliciesFound() throws Exception {
        // Create config with no meeting policies
        ObjectNode emptyConfig = objectMapper.createObjectNode();
        emptyConfig.putArray("meeting_policies");

        TeamsAnonymousChatRemediator emptyRemediator = 
            new TeamsAnonymousChatRemediator(mockClient, emptyConfig, mockRemediationConfig);

        CompletableFuture<PolicyChangeResult> result = emptyRemediator.remediate_();
        PolicyChangeResult changeResult = result.get();

        assertEquals(RemediationResult.FAILED, changeResult.getResult());
        assertEquals("MS.TEAMS.1.8v1", changeResult.getPolicyId());
        assertEquals("No policies found in configuration", changeResult.getDesc());
        
        verify(mockClient, never()).execute(any(CsTeamsCommand.class));
    }

    @Test
    void testRemediateCompleteFailure() throws Exception {
        // Mock all PowerShell executions to fail
        when(mockClient.execute(any()))
            .thenReturn(CompletableFuture.failedFuture(new RuntimeException("PowerShell Error")));

        CompletableFuture<PolicyChangeResult> result = remediator.remediate_();
        PolicyChangeResult changeResult = result.get();

        assertEquals(RemediationResult.FAILED, changeResult.getResult());
        assertEquals("MS.TEAMS.1.8v1", changeResult.getPolicyId());
        assertEquals("Failed to secure any meeting policies", changeResult.getDesc());
        
        verify(mockClient, times(2)).execute(any());
    }

    @Test
    void testRollbackSuccess() throws Exception {
        // Create a change result with parameter changes
        ParameterChangeResult change1 = new ParameterChangeResult()
            .parameter("meetingChatEnabledType: Global")
            .prevValue("Enabled")
            .newValue("EnabledExceptAnonymous")
            .status(ParameterChangeStatus.SUCCESS);

        ParameterChangeResult change2 = new ParameterChangeResult()
            .parameter("meetingChatEnabledType: TestPolicy")
            .prevValue("Enabled")
            .newValue("EnabledExceptAnonymous")
            .status(ParameterChangeStatus.SUCCESS);

        PolicyChangeResult originalResult = new PolicyChangeResult()
            .result(RemediationResult.SUCCESS)
            .policyId("MS.TEAMS.1.8v1")
            .changes(Arrays.asList(change1, change2));

        // Mock successful rollback
        when(mockClient.execute(any()))
            .thenReturn(CompletableFuture.completedFuture(java.util.Collections.emptyList()));

        CompletableFuture<PolicyChangeResult> rollbackResult = remediator.rollback(originalResult);
        PolicyChangeResult rollbackChangeResult = rollbackResult.get();

        assertEquals(RemediationResult.SUCCESS, rollbackChangeResult.getResult());
        assertEquals("MS.TEAMS.1.8v1", rollbackChangeResult.getPolicyId());

        // Verify rollback commands were executed
        verify(mockClient, times(2)).execute(any());
    }

    @Test
    void testRollbackWithFailedChange() throws Exception {
        // Create a change result with one failed parameter change
        ParameterChangeResult successChange = new ParameterChangeResult()
            .parameter("meetingChatEnabledType: Global")
            .prevValue("Enabled")
            .newValue("EnabledExceptAnonymous")
            .status(ParameterChangeStatus.SUCCESS);

        ParameterChangeResult failedChange = new ParameterChangeResult()
            .parameter("meetingChatEnabledType: TestPolicy")
            .prevValue("Enabled")
            .newValue("EnabledExceptAnonymous")
            .status(ParameterChangeStatus.FAILED);

        PolicyChangeResult originalResult = new PolicyChangeResult()
            .result(RemediationResult.PARTIAL_SUCCESS)
            .policyId("MS.TEAMS.1.8v1")
            .changes(Arrays.asList(successChange, failedChange));

        // Mock successful rollback for the successful change
        when(mockClient.execute(any()))
            .thenReturn(CompletableFuture.completedFuture(java.util.Collections.emptyList()));

        CompletableFuture<PolicyChangeResult> rollbackResult = remediator.rollback(originalResult);
        PolicyChangeResult rollbackChangeResult = rollbackResult.get();

        assertEquals(RemediationResult.PARTIAL_SUCCESS, rollbackChangeResult.getResult());

        // Only the successful change should be rolled back
        verify(mockClient, times(1)).execute(any());
    }

    @Test
    void testConstructorWithClientOnly() {
        TeamsAnonymousChatRemediator simpleRemediator = new TeamsAnonymousChatRemediator(mockClient);
        assertEquals("MS.TEAMS.1.8v1", simpleRemediator.getPolicyId());
    }

    @Test
    void testFilterTagPolicies() throws Exception {
        // The Tag: policies should be filtered out and not processed
        // Only Global and TestPolicy should be processed (not Tag:CompliantPolicy)
        when(mockClient.execute(any()))
            .thenReturn(CompletableFuture.completedFuture(java.util.Collections.emptyList()));

        CompletableFuture<PolicyChangeResult> result = remediator.remediate_();
        PolicyChangeResult changeResult = result.get();

        assertEquals(RemediationResult.SUCCESS, changeResult.getResult());
        
        // Verify only 2 policies were processed (Global and TestPolicy)
        // Tag:CompliantPolicy should be filtered out
        assertEquals(2, changeResult.getChanges().size());
        verify(mockClient, times(2)).execute(any());
    }

    @Test
    void testMeetingChatEnabledTypeProperty() throws Exception {
        when(mockClient.execute(any()))
            .thenReturn(CompletableFuture.completedFuture(java.util.Collections.emptyList()));

        CompletableFuture<PolicyChangeResult> result = remediator.remediate_();
        PolicyChangeResult changeResult = result.get();

        // Verify the correct property values are being set
        for (ParameterChangeResult change : changeResult.getChanges()) {
            assertEquals("EnabledExceptAnonymous", change.getNewValue());
            assertEquals("Enabled", change.getPrevValue());
            assertTrue(change.getParameter().contains("meetingChatEnabledType"));
        }
    }
}