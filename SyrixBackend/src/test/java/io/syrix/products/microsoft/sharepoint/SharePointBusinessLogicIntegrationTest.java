package io.syrix.products.microsoft.sharepoint;

import com.fasterxml.jackson.databind.ObjectMapper;
import io.syrix.products.microsoft.sharepoint.model.SharePointTenantProperties;

import io.syrix.protocols.client.sharepoint.powershell.cscom.command.mstypes.AllowToBeDeleted;
import io.syrix.protocols.client.sharepoint.powershell.cscom.command.mstypes.TenantProperties;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Integration test for SharePoint business logic without mocking the core transformation
 * Tests real property extraction and model transformation for malware protection (CIS 7.3.1)
 */
@DisplayName("SharePoint Business Logic Integration Tests")
class SharePointBusinessLogicIntegrationTest {

    private ObjectMapper objectMapper;
    
    @BeforeEach
    void setUp() {
        objectMapper = new ObjectMapper();
    }

    @Test
    @DisplayName("Should correctly transform tenant properties with malware protection enabled")
    void shouldTransformTenantPropertiesWithMalwareProtectionEnabled() {
        // Given - real TenantProperties with malware protection enabled (CIS 7.3.1)
        TenantProperties realTenantProps = new TenantProperties();
        realTenantProps.disallowInfectedFileDownload = true;
        realTenantProps.sharingCapability = 0; // ONLYPEOPLEINORG
        realTenantProps.defaultSharingLinkType = 1; // Specific People
        realTenantProps.defaultLinkPermission = 1; // View only
        realTenantProps.odbSharingCapability = 3; // Existing Guests
        realTenantProps.requireAcceptingAccountMatchInvitedAccount = false;
        
        AllowToBeDeleted realAllowToBeDeleted = new AllowToBeDeleted();
        realAllowToBeDeleted.allowToBeDeletedSPO = true;
        realAllowToBeDeleted.allowToBeDeletedODB = false;

        // When - apply real business logic transformation (no mocking)
        SharePointTenantProperties result = new SharePointTenantProperties(
            realTenantProps, 
            realAllowToBeDeleted.allowToBeDeletedSPO, 
            realAllowToBeDeleted.allowToBeDeletedODB
        );

        // Then - verify business logic correctly preserves all security settings
        assertTrue(result.disallowInfectedFileDownload, "CIS 7.3.1: Malware protection must be preserved");
        assertEquals(0, result.sharingCapability, "Most restrictive sharing capability should be preserved");
        assertEquals(1, result.defaultSharingLinkType, "Specific people link type should be preserved");  
        assertEquals(1, result.defaultLinkPermission, "View-only permission should be preserved");
        assertEquals(3, result.oneDriveSharingCapability, "OneDrive sharing capability should be preserved");
        assertTrue(result.allowFilesWithKeepLabelToBeDeletedSPO, "SPO deletion flag should be preserved");
        assertFalse(result.allowFilesWithKeepLabelToBeDeletedODB, "ODB deletion flag should be preserved");
    }

    @Test
    @DisplayName("Should correctly extract malware protection using direct property access")
    void shouldExtractMalwareProtectionUsingDirectPropertyAccess() {
        // Given - real tenant properties with various security settings
        TenantProperties tenantProps = new TenantProperties();
        tenantProps.disallowInfectedFileDownload = true;
        tenantProps.sharingDomainRestrictionMode = 1;
        tenantProps.emailAttestationRequired = true;
        tenantProps.emailAttestationReAuthDays = 30;
        tenantProps.requireAcceptingAccountMatchInvitedAccount = false;

        // When - access properties directly (no utility needed)
        Boolean malwareProtection = tenantProps.disallowInfectedFileDownload;
        Integer domainRestriction = tenantProps.sharingDomainRestrictionMode;
        Boolean emailAttestation = tenantProps.emailAttestationRequired;

        // Then - verify real business logic works correctly
        assertTrue(malwareProtection, "Should extract malware protection as enabled");
        assertEquals(1, domainRestriction, "Should extract domain restriction as enabled");
        assertTrue(emailAttestation, "Should extract email attestation as enabled");
    }

    @Test
    @DisplayName("Should handle missing malware protection property with default value")
    void shouldHandleMissingMalwareProtectionPropertyWithDefault() {
        // Given - tenant properties without malware protection setting
        TenantProperties tenantProps = new TenantProperties();
        tenantProps.sharingCapability = 2; // Anyone
        tenantProps.defaultSharingLinkType = 2; // Organization
        tenantProps.requireAcceptingAccountMatchInvitedAccount = false;
        // disallowInfectedFileDownload is null by default

        // When - check malware protection (null means not set)
        Boolean malwareProtection = tenantProps.disallowInfectedFileDownload;

        // Then - should be null/false, indicating non-compliant configuration
        assertNull(malwareProtection, "Missing malware protection should be null (non-compliant)");
    }

    @Test
    @DisplayName("Should validate CIS 7.3.1 compliance through complete transformation")
    void shouldValidateCIS731ComplianceThroughCompleteTransformation() {
        // Given - tenant configured for CIS 7.3.1 compliance
        TenantProperties cisCompliantTenant = new TenantProperties();
        cisCompliantTenant.disallowInfectedFileDownload = true;
        cisCompliantTenant.sharingCapability = 0; // Most restrictive
        cisCompliantTenant.defaultSharingLinkType = 1; // Specific People
        cisCompliantTenant.defaultLinkPermission = 1; // View only
        cisCompliantTenant.sharingDomainRestrictionMode = 1; // Enabled
        cisCompliantTenant.emailAttestationRequired = true; // Enabled
        cisCompliantTenant.emailAttestationReAuthDays = 30; // Max 30 days
        cisCompliantTenant.requireAcceptingAccountMatchInvitedAccount = false;
        
        AllowToBeDeleted allowToBeDeleted = new AllowToBeDeleted();
        allowToBeDeleted.allowToBeDeletedSPO = true;
        allowToBeDeleted.allowToBeDeletedODB = true;

        // When - apply complete business logic transformation
        SharePointTenantProperties result = new SharePointTenantProperties(
            cisCompliantTenant, 
            allowToBeDeleted.allowToBeDeletedSPO, 
            allowToBeDeleted.allowToBeDeletedODB
        );

        // Then - verify complete CIS 7.3.1 compliance is maintained
        assertTrue(result.disallowInfectedFileDownload, "CIS 7.3.1: Infected files must be disallowed for download");
        assertEquals(0, result.sharingCapability, "CIS: Most restrictive external sharing");
        assertEquals(1, result.defaultSharingLinkType, "CIS: Default sharing links to specific people");
        assertEquals(1, result.defaultLinkPermission, "CIS: Default link permission to view only");
        
        // Verify extracted properties maintain values through transformation
        Boolean emailAttestation = cisCompliantTenant.emailAttestationRequired;
        Integer reAuthDays = cisCompliantTenant.emailAttestationReAuthDays;

        assertTrue(emailAttestation, "CIS: Email attestation should be enabled");
        assertEquals(30, reAuthDays, "CIS: Re-authentication should be 30 days or less");
    }

    @Test
    @DisplayName("Should detect non-compliant configuration through business logic")
    void shouldDetectNonCompliantConfigurationThroughBusinessLogic() {
        // Given - tenant with non-compliant security settings
        TenantProperties nonCompliantTenant = new TenantProperties();
        nonCompliantTenant.disallowInfectedFileDownload = false; // Non-compliant!
        nonCompliantTenant.sharingCapability = 2; // Anyone - risky
        nonCompliantTenant.defaultSharingLinkType = 2; // Organization - risky
        nonCompliantTenant.defaultLinkPermission = 2; // Edit - risky
        nonCompliantTenant.requireAcceptingAccountMatchInvitedAccount = false;
        
        AllowToBeDeleted allowToBeDeleted = new AllowToBeDeleted();
        allowToBeDeleted.allowToBeDeletedSPO = true;
        allowToBeDeleted.allowToBeDeletedODB = true;

        // When - apply business logic transformation
        SharePointTenantProperties result = new SharePointTenantProperties(
            nonCompliantTenant, 
            allowToBeDeleted.allowToBeDeletedSPO, 
            allowToBeDeleted.allowToBeDeletedODB
        );

        // Then - verify business logic correctly identifies non-compliant settings
        assertFalse(result.disallowInfectedFileDownload, "Should detect malware protection is disabled");
        assertEquals(2, result.sharingCapability, "Should detect overly permissive sharing (Anyone)");
        assertEquals(2, result.defaultSharingLinkType, "Should detect overly permissive link type (Organization)");
        assertEquals(2, result.defaultLinkPermission, "Should detect overly permissive permissions (Edit)");
        
        // This configuration would fail CIS 7.3.1 compliance checks
        assertFalse(isCIS731Compliant(result), "Configuration should be identified as non-compliant with CIS 7.3.1");
    }

    /**
     * Helper method to determine CIS 7.3.1 compliance based on business logic
     */
    private boolean isCIS731Compliant(SharePointTenantProperties tenant) {
        return tenant.disallowInfectedFileDownload && // CIS 7.3.1 requirement
               tenant.sharingCapability <= 3 && // Restrictive sharing
               tenant.defaultSharingLinkType == 1 && // Specific people
               tenant.defaultLinkPermission == 1; // View only
    }
}