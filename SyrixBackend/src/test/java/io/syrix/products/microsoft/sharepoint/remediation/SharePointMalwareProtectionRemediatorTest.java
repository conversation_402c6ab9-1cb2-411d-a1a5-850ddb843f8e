package io.syrix.products.microsoft.sharepoint.remediation;

import com.fasterxml.jackson.databind.JsonNode;

import io.syrix.products.microsoft.base.ParameterChangeResult;
import io.syrix.products.microsoft.base.ParameterChangeStatus;
import io.syrix.products.microsoft.base.PolicyChangeResult;
import io.syrix.products.microsoft.base.RemediationResult;
import io.syrix.datamodel.task.remediation.sharepoint.SharepointRemediationConfig;
import io.syrix.products.microsoft.sharepoint.model.SharePointTenantProperties;
import io.syrix.products.microsoft.sharepoint.SharepointConstants;
import io.syrix.protocols.client.PowerShellSharepointClient;
import io.syrix.protocols.client.sharepoint.powershell.cscom.command.SPShellCommand;
import io.syrix.protocols.client.sharepoint.powershell.cscom.command.ShellCommandResult;
import io.syrix.protocols.client.sharepoint.powershell.cscom.command.mstypes.GeneralResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.concurrent.CompletableFuture;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * Unit tests for SharePointMalwareProtectionRemediator
 * Tests CIS 7.3.1: Ensure Office 365 SharePoint infected files are disallowed for download
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("SharePoint Malware Protection Remediator Tests")
class SharePointMalwareProtectionRemediatorTest {

    @Mock
    private PowerShellSharepointClient mockClient;

    @Mock
    private SharePointTenantProperties mockTenant;

    @Mock
    private SharepointRemediationConfig mockConfig;

    @Mock
    private GeneralResult mockGeneralResult;

    @Captor
    private ArgumentCaptor<SPShellCommand<GeneralResult>> commandCaptor;

    private SharePointMalwareProtectionRemediator remediator;

    @BeforeEach
    void setUp() {
        remediator = new SharePointMalwareProtectionRemediator(mockClient, mockTenant, mockConfig);
    }

    @Nested
    @DisplayName("Remediation Tests")
    class RemediationTests {

        @Test
        @DisplayName("Should succeed when malware protection is already enabled")
        void shouldSucceedWhenAlreadyEnabled() {
            // Given
            mockTenant.disallowInfectedFileDownload = true;

            // When
            CompletableFuture<PolicyChangeResult> result = remediator.remediate_();

            // Then
            PolicyChangeResult changeResult = result.join();
            assertThat(changeResult.getResult()).isEqualTo(RemediationResult.SUCCESS);
            assertThat(changeResult.getPolicyId()).isEqualTo("MS.SHAREPOINT.5.1v1");
            assertThat(changeResult.getDesc()).isEqualTo(SharepointConstants.MALWARE_PROTECTION_SUCCESS);
        }

        @Test
        @DisplayName("Should fail when configuration is missing")
        void shouldFailWhenConfigurationMissing() {
            // Given
            remediator = new SharePointMalwareProtectionRemediator(mockClient, mockTenant, null);

            // When
            CompletableFuture<PolicyChangeResult> result = remediator.remediate_();

            // Then
            PolicyChangeResult changeResult = result.join();
            assertThat(changeResult.getResult()).isEqualTo(RemediationResult.FAILED);
            assertThat(changeResult.getPolicyId()).isEqualTo("MS.SHAREPOINT.5.1v1");
            assertThat(changeResult.getDesc()).isEqualTo("SharePoint remediation configuration is missing");
        }

        @Test
        @DisplayName("Should successfully enable malware protection when disabled")
        void shouldSuccessfullyEnableMalwareProtectionWhenDisabled() {
            // Given
            mockTenant.disallowInfectedFileDownload = false;
            mockTenant.objectIdentity = "test-tenant-id";

            when(mockClient.execute_(any())).thenAnswer(invocation -> {
                @SuppressWarnings("unchecked")
                ShellCommandResult<GeneralResult> result = ShellCommandResult.of(Collections.singletonList(mockGeneralResult), invocation.getArgument(0));
                return CompletableFuture.completedFuture(result);
            });
            // Set errorInfo to null to indicate success (no error)
            mockGeneralResult.errorInfo = null;

            // When
            CompletableFuture<PolicyChangeResult> result = remediator.remediate_();

            // Then
            assertThat(result.isCompletedExceptionally()).isFalse();
            verify(mockClient).execute_(commandCaptor.capture());

            SPShellCommand<GeneralResult> capturedCommand = commandCaptor.getValue();
            assertThat(capturedCommand).isNotNull();
        }

        @Test
        @DisplayName("Should fail when tenant properties are not available")
        void shouldFailWhenTenantPropertiesNotAvailable() {
            // Given
            remediator = new SharePointMalwareProtectionRemediator(mockClient, null, mockConfig);

            // When
            CompletableFuture<PolicyChangeResult> result = remediator.remediate_();

            // Then
            PolicyChangeResult changeResult = result.join();
            assertThat(changeResult.getResult()).isEqualTo(RemediationResult.FAILED);
            assertThat(changeResult.getPolicyId()).isEqualTo("MS.SHAREPOINT.5.1v1");
            assertThat(changeResult.getDesc()).isEqualTo("SharePoint tenant properties are not available");
        }

        @Test
        @DisplayName("Should successfully enable malware protection when null")
        void shouldSuccessfullyEnableMalwareProtectionWhenNull() {
            // Given
            mockTenant.disallowInfectedFileDownload = null;
            mockTenant.objectIdentity = "test-tenant-id";

            when(mockClient.execute_(any())).thenAnswer(invocation -> {
                @SuppressWarnings("unchecked")
                ShellCommandResult<GeneralResult> result = ShellCommandResult.of(Collections.singletonList(mockGeneralResult), invocation.getArgument(0));
                return CompletableFuture.completedFuture(result);
            });
            // Set errorInfo to null to indicate success (no error)
            mockGeneralResult.errorInfo = null;

            // When
            CompletableFuture<PolicyChangeResult> result = remediator.remediate_();

            // Then
            assertThat(result.isCompletedExceptionally()).isFalse();
            verify(mockClient).execute_(commandCaptor.capture());

            SPShellCommand<GeneralResult> capturedCommand = commandCaptor.getValue();
            assertThat(capturedCommand).isNotNull();
        }

        @Test
        @DisplayName("Should handle execution exception gracefully")
        void shouldHandleExecutionExceptionGracefully() {
            // Given
            mockTenant.disallowInfectedFileDownload = false;
            mockTenant.objectIdentity = "test-tenant-id";

            RuntimeException testException = new RuntimeException("Test exception");
            when(mockClient.execute_(any())).thenReturn(CompletableFuture.failedFuture(testException));

            // When
            CompletableFuture<PolicyChangeResult> result = remediator.remediate_();

            // Then
            PolicyChangeResult changeResult = result.join();
            assertThat(changeResult.getResult()).isEqualTo(RemediationResult.FAILED);
            assertThat(changeResult.getPolicyId()).isEqualTo("MS.SHAREPOINT.5.1v1");
            assertThat(changeResult.getDesc()).contains("Failed to enable malware protection");
        }
    }

    @Nested
    @DisplayName("Rollback Tests")
    class RollbackTests {

        @Test
        @DisplayName("Should fail rollback when fix result is null")
        void shouldFailRollbackWhenFixResultIsNull() {
            // Given
            SharePointMalwareProtectionRemediator rollbackRemediator = 
                new SharePointMalwareProtectionRemediator(mockClient, mockTenant);

            // When
            CompletableFuture<PolicyChangeResult> result = rollbackRemediator.rollback(null);

            // Then
            PolicyChangeResult changeResult = result.join();
            assertThat(changeResult.getResult()).isEqualTo(RemediationResult.FAILED);
            assertThat(changeResult.getPolicyId()).isEqualTo("MS.SHAREPOINT.5.1v1");
            assertThat(changeResult.getDesc()).isEqualTo("No fix result provided for rollback");
        }

        @Test
        @DisplayName("Should fail rollback when no changes to rollback")
        void shouldFailRollbackWhenNoChanges() {
            // Given
            SharePointMalwareProtectionRemediator rollbackRemediator = 
                new SharePointMalwareProtectionRemediator(mockClient, mockTenant);
            
            PolicyChangeResult fixResult = new PolicyChangeResult()
                .changes(Collections.emptyList());

            // When
            CompletableFuture<PolicyChangeResult> result = rollbackRemediator.rollback(fixResult);

            // Then
            PolicyChangeResult changeResult = result.join();
            assertThat(changeResult.getResult()).isEqualTo(RemediationResult.FAILED);
            assertThat(changeResult.getPolicyId()).isEqualTo("MS.SHAREPOINT.5.1v1");
            assertThat(changeResult.getDesc()).isEqualTo("No changes to rollback");
        }

        @Test
        @DisplayName("Should fail rollback when tenant properties not available")
        void shouldFailRollbackWhenTenantNotAvailable() {
            // Given
            SharePointMalwareProtectionRemediator rollbackRemediator = 
                new SharePointMalwareProtectionRemediator(mockClient, null);
            
            PolicyChangeResult fixResult = new PolicyChangeResult();
            ParameterChangeResult paramChange = new ParameterChangeResult()
                .prevValue(false)
                .newValue(true);
            fixResult.changes(Collections.singletonList(paramChange));

            // When
            CompletableFuture<PolicyChangeResult> result = rollbackRemediator.rollback(fixResult);

            // Then
            PolicyChangeResult changeResult = result.join();
            assertThat(changeResult.getResult()).isEqualTo(RemediationResult.FAILED);
            assertThat(changeResult.getPolicyId()).isEqualTo("MS.SHAREPOINT.5.1v1");
            assertThat(changeResult.getDesc()).isEqualTo("SharePoint tenant properties not available for rollback");
        }

        @Test
        @DisplayName("Should successfully rollback changes")
        void shouldSuccessfullyRollbackChanges() {
            // Given
            SharePointMalwareProtectionRemediator rollbackRemediator = 
                new SharePointMalwareProtectionRemediator(mockClient, mockTenant);
            
            mockTenant.objectIdentity = "test-tenant-id";

            PolicyChangeResult fixResult = new PolicyChangeResult();
            ParameterChangeResult paramChange = new ParameterChangeResult()
                .prevValue(false)  // Previous value was false
                .newValue(true);   // New value was true
            fixResult.changes(Collections.singletonList(paramChange));

            PolicyChangeResult successResult = new PolicyChangeResult()
                .result(RemediationResult.SUCCESS)
                .policyId("MS.SHAREPOINT.5.1v1");

            when(mockClient.execute_(any())).thenAnswer(invocation -> {
                @SuppressWarnings("unchecked")
                ShellCommandResult<GeneralResult> result = ShellCommandResult.of(Collections.singletonList(mockGeneralResult), invocation.getArgument(0));
                return CompletableFuture.completedFuture(result);
            });
            // Set errorInfo to null to indicate success (no error)
            mockGeneralResult.errorInfo = null;

            // When
            CompletableFuture<PolicyChangeResult> result = rollbackRemediator.rollback(fixResult);

            // Then
            assertThat(result.isCompletedExceptionally()).isFalse();
            verify(mockClient).execute_(commandCaptor.capture());

            SPShellCommand<GeneralResult> capturedCommand = commandCaptor.getValue();
            assertThat(capturedCommand).isNotNull();
        }
    }

    @Nested
    @DisplayName("JSON Remediation Tests")
    class JsonRemediationTests {

        @Test
        @DisplayName("Should return JSON representation of remediation result")
        void shouldReturnJsonRepresentation() {
            // Given
            mockTenant.disallowInfectedFileDownload = true;

            // When
            CompletableFuture<JsonNode> result = remediator.remediate();

            // Then
            JsonNode jsonResult = result.join();
            assertThat(jsonResult).isNotNull();
            assertThat(jsonResult.get("Result").asText()).isEqualTo("SUCCESS");
            assertThat(jsonResult.get("PolicyId").asText()).isEqualTo("MS.SHAREPOINT.5.1v1");
        }
    }

    @Nested
    @DisplayName("Policy ID Tests")
    class PolicyIdTests {

        @Test
        @DisplayName("Should return correct policy ID")
        void shouldReturnCorrectPolicyId() {
            // When
            String policyId = remediator.getPolicyId();

            // Then
            assertThat(policyId).isEqualTo("MS.SHAREPOINT.5.1v1");
        }
    }
}