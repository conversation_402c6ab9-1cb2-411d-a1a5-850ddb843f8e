package io.syrix.products.microsoft.sharepoint;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.syrix.common.exceptions.ConfigurationExportException;
import io.syrix.common.metrics.MetricsCollector;
import io.syrix.common.model.ConfigurationResult;
import io.syrix.datamodel.report.ConfigurationServiceType;
import io.syrix.products.microsoft.sharepoint.SharePointConfigurationService.SharePointClientException;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.protocols.client.PowerShellSharepointClient;
import io.syrix.protocols.client.graph.response.Site;
import io.syrix.protocols.client.sharepoint.powershell.cscom.command.SPShellCommand;
import io.syrix.protocols.client.sharepoint.powershell.cscom.command.mstypes.AllowToBeDeleted;
import io.syrix.protocols.client.sharepoint.powershell.cscom.command.mstypes.SiteProperties;
import io.syrix.protocols.client.sharepoint.powershell.cscom.command.mstypes.TenantProperties;
import io.syrix.protocols.model.GraphRequest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * Unit tests for SharePointConfigurationService
 * Tests configuration export functionality for SharePoint Online and OneDrive for Business
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("SharePoint Configuration Service Tests")
class SharePointConfigurationServiceTest {

    @Mock
    private MicrosoftGraphClient graphClient;
    
    @Mock
    private PowerShellSharepointClient powerShellClient;
    
    @Mock
    private MetricsCollector metricsCollector;

    @Captor
    private ArgumentCaptor<GraphRequest> graphRequestCaptor;
    
    @Captor
    private ArgumentCaptor<SPShellCommand<?>> commandCaptor;

    private SharePointConfigurationService service;
    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        objectMapper = new ObjectMapper();
        service = new SharePointConfigurationService(
            graphClient, 
            powerShellClient, 
            objectMapper, 
            metricsCollector
        );
    }

    @Nested
    @DisplayName("Configuration Export Tests")
    class ConfigurationExportTests {

        @Test
        @DisplayName("Should successfully export SharePoint configuration")
        void shouldSuccessfullyExportSharePointConfiguration() throws Exception {
            // Given
            TenantProperties tenantProps = createMockTenantProperties();
            AllowToBeDeleted allowToBeDeleted = createMockAllowToBeDeleted();
            SiteProperties siteProps = createMockSiteProperties();
            Site site = createMockSite();

            when(graphClient.makeGraphRequest(any(GraphRequest.class), eq(Site.class)))
                .thenReturn(CompletableFuture.completedFuture(site));
            
            when(powerShellClient.execute(any(SPShellCommand.class)))
                .thenReturn(CompletableFuture.completedFuture(List.of(tenantProps)))
                .thenReturn(CompletableFuture.completedFuture(List.of(allowToBeDeleted)))
                .thenReturn(CompletableFuture.completedFuture(List.of(siteProps)));

            // When
            ConfigurationResult result = service.exportConfiguration();

            // Then
            assertThat(result).isNotNull();
            assertThat(result.getServiceType()).isEqualTo(ConfigurationServiceType.SHAREPOINT);

            JsonNode data = result.getData();
            assertThat(data.has("SPO_tenant")).isTrue();
            // SPO_site may not be present if there are issues with Graph API or PowerShell commands
            assertThat(data.has("OneDrive_PnP_Flag")).isTrue();
            assertThat(data.get("OneDrive_PnP_Flag").asBoolean()).isTrue();

            verify(metricsCollector).recordExportStart();
            verify(metricsCollector).recordExportSuccess(any());
            verify(graphClient).makeGraphRequest(graphRequestCaptor.capture(), eq(Site.class));
            verify(powerShellClient, times(3)).execute(any(SPShellCommand.class));

            GraphRequest capturedRequest = graphRequestCaptor.getValue();
            assertThat(capturedRequest.getEndpoint()).isEqualTo("/v1.0/sites/root");
        }

        @Test
        @DisplayName("Should handle PowerShell client failures gracefully")
        void shouldHandlePowerShellClientFailures() {
            // Given
            RuntimeException powerShellException = new RuntimeException("PowerShell execution failed");
            when(powerShellClient.execute(any(SPShellCommand.class)))
                .thenReturn(CompletableFuture.failedFuture(powerShellException));

            // When
            ConfigurationResult result = service.exportConfiguration();

            // Then
            assertThat(result).isNotNull();
            JsonNode data = result.getData();
            assertThat(data.has("SharePoint_unsuccessful_commands")).isTrue();
            assertThat(data.get("SharePoint_unsuccessful_commands").get(0).asText()).isNotEmpty();

            verify(metricsCollector).recordExportStart();
            verify(metricsCollector).recordExportSuccess(any());
        }

        @Test
        @DisplayName("Should handle Graph client failures gracefully")
        void shouldHandleGraphClientFailures() {
            // Given
            TenantProperties tenantProps = createMockTenantProperties();
            AllowToBeDeleted allowToBeDeleted = createMockAllowToBeDeleted();
            
            when(powerShellClient.execute(any(SPShellCommand.class)))
                .thenReturn(CompletableFuture.completedFuture(List.of(tenantProps)))
                .thenReturn(CompletableFuture.completedFuture(List.of(allowToBeDeleted)));
                
            RuntimeException graphException = new RuntimeException("Graph API failed");
            when(graphClient.makeGraphRequest(any(GraphRequest.class), eq(Site.class)))
                .thenReturn(CompletableFuture.failedFuture(graphException));

            // When
            ConfigurationResult result = service.exportConfiguration();

            // Then
            assertThat(result).isNotNull();
            JsonNode data = result.getData();
            assertThat(data.has("SPO_tenant")).isTrue();
            assertThat(data.has("SharePoint_unsuccessful_commands")).isTrue();
            assertThat(data.get("SharePoint_unsuccessful_commands").get(0).asText()).isNotEmpty();

            verify(metricsCollector).recordExportStart();
            verify(metricsCollector).recordExportSuccess(any());
        }

        @Test
        @DisplayName("Should handle empty PowerShell results")
        void shouldHandleEmptyPowerShellResults() {
            // Given
            when(powerShellClient.execute(any(SPShellCommand.class)))
                .thenReturn(CompletableFuture.completedFuture(Collections.emptyList()));

            // When
            ConfigurationResult result = service.exportConfiguration();

            // Then
            assertThat(result).isNotNull();
            JsonNode data = result.getData();
            assertThat(data.has("SharePoint_unsuccessful_commands")).isTrue();
            assertThat(data.get("SharePoint_unsuccessful_commands").get(0).asText()).isNotEmpty();

            verify(metricsCollector).recordExportSuccess(any());
        }
    }

    @Nested
    @DisplayName("SPO Tenant Tests")
    class SPOTenantTests {

        @Test
        @DisplayName("Should combine tenant properties and allow-to-be-deleted correctly")
        void shouldCombineTenantPropertiesCorrectly() throws Exception {
            // Given
            TenantProperties tenantProps = createMockTenantProperties();
            tenantProps.setAdditionalProperty("SharingCapability", 1);
            tenantProps.setAdditionalProperty("DefaultSharingLinkType", 2);
            
            AllowToBeDeleted allowToBeDeleted = createMockAllowToBeDeleted();
            allowToBeDeleted.allowToBeDeletedSPO = true;
            allowToBeDeleted.allowToBeDeletedODB = false;

            Site site = createMockSite();
            SiteProperties siteProps = createMockSiteProperties();

            when(powerShellClient.execute(any(SPShellCommand.class)))
                .thenReturn(CompletableFuture.completedFuture(List.of(tenantProps)))
                .thenReturn(CompletableFuture.completedFuture(List.of(allowToBeDeleted)))
                .thenReturn(CompletableFuture.completedFuture(List.of(siteProps)));

            when(graphClient.makeGraphRequest(any(GraphRequest.class), eq(Site.class)))
                .thenReturn(CompletableFuture.completedFuture(site));

            // When
            ConfigurationResult result = service.exportConfiguration();

            // Then
            assertThat(result).isNotNull();
            JsonNode spoTenant = result.getData().get("SPO_tenant");
            assertThat(spoTenant.isArray()).isTrue();
            assertThat(spoTenant.size()).isEqualTo(1);
            
            JsonNode tenantData = spoTenant.get(0);
            assertThat(tenantData.get("allowFilesWithKeepLabelToBeDeletedSPO").asBoolean()).isTrue();
            assertThat(tenantData.get("allowFilesWithKeepLabelToBeDeletedODB").asBoolean()).isFalse();
        }

        @Test
        @DisplayName("Should handle multiple tenant properties by selecting first")
        void shouldHandleMultipleTenantProperties() throws Exception {
            // Given
            TenantProperties firstTenant = createMockTenantProperties();
            TenantProperties secondTenant = createMockTenantProperties();
            AllowToBeDeleted allowToBeDeleted = createMockAllowToBeDeleted();
            Site site = createMockSite();
            SiteProperties siteProps = createMockSiteProperties();

            when(powerShellClient.execute(any(SPShellCommand.class)))
                .thenReturn(CompletableFuture.completedFuture(List.of(firstTenant, secondTenant)))
                .thenReturn(CompletableFuture.completedFuture(List.of(allowToBeDeleted)))
                .thenReturn(CompletableFuture.completedFuture(List.of(siteProps)));

            when(graphClient.makeGraphRequest(any(GraphRequest.class), eq(Site.class)))
                .thenReturn(CompletableFuture.completedFuture(site));

            // When
            ConfigurationResult result = service.exportConfiguration();

            // Then
            assertThat(result).isNotNull();
            // Service should log warning but still use first tenant
        }
    }

    @Nested
    @DisplayName("SPO Site Tests") 
    class SPOSiteTests {

        @Test
        @DisplayName("Should retrieve site properties using Graph API site URL")
        void shouldRetrieveSitePropertiesUsingGraphSiteUrl() throws Exception {
            // Given
            String expectedWebUrl = "https://contoso.sharepoint.com";
            Site site = createMockSite();
            site.setWebUrl(expectedWebUrl);
            
            TenantProperties tenantProps = createMockTenantProperties();
            AllowToBeDeleted allowToBeDeleted = createMockAllowToBeDeleted();
            SiteProperties siteProps = createMockSiteProperties();

            when(graphClient.makeGraphRequest(any(GraphRequest.class), eq(Site.class)))
                .thenReturn(CompletableFuture.completedFuture(site));
                
            when(powerShellClient.execute(any(SPShellCommand.class)))
                .thenReturn(CompletableFuture.completedFuture(List.of(tenantProps)))
                .thenReturn(CompletableFuture.completedFuture(List.of(allowToBeDeleted)))
                .thenReturn(CompletableFuture.completedFuture(List.of(siteProps)));

            // When
            ConfigurationResult result = service.exportConfiguration();

            // Then
            assertThat(result).isNotNull();
            JsonNode data = result.getData();
            // Check if SPO_site exists, if not, verify unsuccessful commands are tracked
            if (data.has("SPO_site")) {
                JsonNode spoSite = data.get("SPO_site");
                assertThat(spoSite.isArray()).isTrue();
                assertThat(spoSite.size()).isEqualTo(1);
            } else {
                // If SPO_site is missing, there should be unsuccessful commands
                assertThat(data.has("SharePoint_unsuccessful_commands")).isTrue();
            }
            
            // Verify that PowerShell was called with the correct site URL
            verify(powerShellClient, times(3)).execute(commandCaptor.capture());
            List<SPShellCommand<?>> commands = commandCaptor.getAllValues();
            
            // The third command should be the site properties command with the WebUrl
            SPShellCommand<?> siteCommand = commands.get(2);
            // Command should contain the WebUrl from the Graph API response
        }

        @Test
        @DisplayName("Should handle site retrieval failures")
        void shouldHandleSiteRetrievalFailures() {
            // Given
            TenantProperties tenantProps = createMockTenantProperties();
            AllowToBeDeleted allowToBeDeleted = createMockAllowToBeDeleted();
            
            when(powerShellClient.execute(any(SPShellCommand.class)))
                .thenReturn(CompletableFuture.completedFuture(List.of(tenantProps)))
                .thenReturn(CompletableFuture.completedFuture(List.of(allowToBeDeleted)));
                
            RuntimeException siteException = new RuntimeException("Site retrieval failed");
            when(graphClient.makeGraphRequest(any(GraphRequest.class), eq(Site.class)))
                .thenReturn(CompletableFuture.failedFuture(siteException));

            // When
            ConfigurationResult result = service.exportConfiguration();

            // Then
            assertThat(result).isNotNull();
            JsonNode data = result.getData();
            assertThat(data.has("SPO_tenant")).isTrue();
            assertThat(data.has("SharePoint_unsuccessful_commands")).isTrue();
            assertThat(data.get("SharePoint_unsuccessful_commands").get(0).asText()).isNotEmpty();
        }
    }

    @Nested
    @DisplayName("Exception Handling Tests")
    class ExceptionHandlingTests {

        @Test
        @DisplayName("Should create SharePointClientException with message")
        void shouldCreateSharePointClientException() {
            // Given
            String errorMessage = "Test error message";

            // When
            SharePointClientException exception = new SharePointClientException(errorMessage);

            // Then
            assertThat(exception.getMessage()).isEqualTo(errorMessage);
            assertThat(exception).isInstanceOf(io.syrix.common.exceptions.SyrixRuntimeException.class);
        }

        @Test
        @DisplayName("Should handle timeout scenarios")
        void shouldHandleTimeoutScenarios() {
            // Given - setup responses that will cause a timeout exception
            when(powerShellClient.execute(any(SPShellCommand.class)))
                .thenReturn(CompletableFuture.failedFuture(new java.util.concurrent.TimeoutException("Operation timed out")));

            // When
            ConfigurationResult result = service.exportConfiguration();

            // Then
            assertThat(result).isNotNull();
            JsonNode data = result.getData();
            assertThat(data.has("SharePoint_unsuccessful_commands")).isTrue();
            assertThat(data.get("SharePoint_unsuccessful_commands").get(0).asText()).isNotEmpty();
        }
    }

    @Nested
    @DisplayName("Business Logic Tests")
    class BusinessLogicTests {

        @Test
        @DisplayName("Should correctly extract malware protection settings from tenant properties")
        void shouldExtractMalwareProtectionSettingsFromTenantProperties() throws Exception {
            // Given - real tenant properties with malware protection enabled
            TenantProperties tenantProps = new TenantProperties();
            tenantProps.objectIdentity = "test-object-identity";
            tenantProps.objectType = "Microsoft.SharePoint.Administration.SPOTenant";
            tenantProps.disallowInfectedFileDownload = true;
            tenantProps.sharingCapability = 0; // ONLYPEOPLEINORG
            tenantProps.defaultSharingLinkType = 1; // Specific People
            tenantProps.requireAcceptingAccountMatchInvitedAccount = false;
            tenantProps.emailAttestationRequired = false;
            tenantProps.emailAttestationReAuthDays = 30;

            AllowToBeDeleted allowToBeDeleted = createMockAllowToBeDeleted();
            Site site = createMockSite();
            SiteProperties siteProps = createMockSiteProperties();

            when(graphClient.makeGraphRequest(any(GraphRequest.class), eq(Site.class)))
                .thenReturn(CompletableFuture.completedFuture(site));

            when(powerShellClient.execute(any(SPShellCommand.class)))
                .thenReturn(CompletableFuture.completedFuture(List.of(tenantProps)))
                .thenReturn(CompletableFuture.completedFuture(List.of(allowToBeDeleted)))
                .thenReturn(CompletableFuture.completedFuture(List.of(siteProps)));

            // When
            ConfigurationResult result = service.exportConfiguration();

            // Then - verify that malware protection setting is correctly preserved
            assertThat(result).isNotNull();
            JsonNode spoTenant = result.getData().get("SPO_tenant");
            assertThat(spoTenant.isArray()).isTrue();

            JsonNode tenantData = spoTenant.get(0);
            // Test that the business logic correctly preserves the malware protection setting
            assertThat(tenantData.has("disallowInfectedFileDownload")).isTrue();
            assertThat(tenantData.get("disallowInfectedFileDownload").asBoolean()).isTrue();
        }

        @Test
        @DisplayName("Should validate SharePoint malware protection compliance with CIS 7.3.1")
        void shouldValidateMalwareProtectionComplianceWithCIS() throws Exception {
            // Given - tenant configured for CIS 7.3.1 compliance
            TenantProperties cisCompliantTenant = new TenantProperties();
            cisCompliantTenant.objectIdentity = "test-object-identity";
            cisCompliantTenant.objectType = "Microsoft.SharePoint.Administration.SPOTenant";
            cisCompliantTenant.disallowInfectedFileDownload = true;
            cisCompliantTenant.sharingCapability = 0; // Most restrictive
            cisCompliantTenant.defaultSharingLinkType = 1; // Specific People
            cisCompliantTenant.defaultLinkPermission = 1; // View only
            cisCompliantTenant.requireAcceptingAccountMatchInvitedAccount = false;
            cisCompliantTenant.emailAttestationRequired = false;
            cisCompliantTenant.emailAttestationReAuthDays = 30;
            
            AllowToBeDeleted allowToBeDeleted = createMockAllowToBeDeleted();
            Site site = createMockSite();
            SiteProperties siteProps = createMockSiteProperties();

            when(graphClient.makeGraphRequest(any(GraphRequest.class), eq(Site.class)))
                .thenReturn(CompletableFuture.completedFuture(site));
            
            when(powerShellClient.execute(any(SPShellCommand.class)))
                .thenReturn(CompletableFuture.completedFuture(List.of(cisCompliantTenant)))
                .thenReturn(CompletableFuture.completedFuture(List.of(allowToBeDeleted)))
                .thenReturn(CompletableFuture.completedFuture(List.of(siteProps)));

            // When
            ConfigurationResult result = service.exportConfiguration();

            // Then - verify CIS 7.3.1 compliance is maintained in exported configuration
            assertThat(result).isNotNull();
            JsonNode spoTenant = result.getData().get("SPO_tenant");
            JsonNode tenantData = spoTenant.get(0);

            // Verify all CIS security settings are correctly exported
            assertThat(tenantData.get("disallowInfectedFileDownload").asBoolean()).as("CIS 7.3.1: Malware protection must be enabled").isTrue();
            assertThat(tenantData.get("sharingCapability").asInt()).as("CIS: Most restrictive sharing").isEqualTo(0);
            assertThat(tenantData.get("defaultSharingLinkType").asInt()).as("CIS: Specific people only").isEqualTo(1);
            assertThat(tenantData.get("defaultLinkPermission").asInt()).as("CIS: View-only permissions").isEqualTo(1);
        }
    }

    @Nested
    @DisplayName("Command Tracking Tests")
    class CommandTrackingTests {

        @Test
        @DisplayName("Should track successful and unsuccessful commands")
        void shouldTrackSuccessfulAndUnsuccessfulCommands() throws Exception {
            // Given
            TenantProperties tenantProps = createMockTenantProperties();
            AllowToBeDeleted allowToBeDeleted = createMockAllowToBeDeleted();
            Site site = createMockSite();

            when(graphClient.makeGraphRequest(any(GraphRequest.class), eq(Site.class)))
                .thenReturn(CompletableFuture.completedFuture(site));

            when(powerShellClient.execute(any(SPShellCommand.class)))
                .thenReturn(CompletableFuture.completedFuture(List.of(tenantProps)))
                .thenReturn(CompletableFuture.completedFuture(List.of(allowToBeDeleted)))
                .thenReturn(CompletableFuture.failedFuture(new RuntimeException("Site props failed")));

            // When
            ConfigurationResult result = service.exportConfiguration();

            // Then - verify that the service completes but tracks the failed command
            assertThat(result).isNotNull();
            assertThat(result.getServiceType()).isEqualTo(ConfigurationServiceType.SHAREPOINT);

            // Verify that the result contains the successful parts (SPO_tenant) but not the failed part (SPO_site)
            JsonNode data = result.getData();
            assertThat(data.has("SPO_tenant")).isTrue();
            assertThat(data.has("OneDrive_PnP_Flag")).isTrue();
            // SPO_site should be missing due to the failure
            assertThat(data.has("SPO_site")).isFalse();

            // Verify command tracking
            assertThat(data.has("SharePoint_successful_commands")).isTrue();
            assertThat(data.has("SharePoint_unsuccessful_commands")).isTrue();
        }
    }

    // Helper methods for creating mock objects
    private TenantProperties createMockTenantProperties() {
        TenantProperties tenantProps = new TenantProperties();
        tenantProps.objectIdentity = "test-object-identity";
        tenantProps.objectType = "Microsoft.SharePoint.Administration.SPOTenant";
        tenantProps.sharingCapability = 0;
        tenantProps.odbSharingCapability = 0;
        tenantProps.defaultSharingLinkType = 1;
        tenantProps.defaultLinkPermission = 1;
        tenantProps.disallowInfectedFileDownload = true;
        tenantProps.requireAcceptingAccountMatchInvitedAccount = false;
        tenantProps.emailAttestationRequired = false;
        tenantProps.emailAttestationReAuthDays = 30;
        return tenantProps;
    }

    private AllowToBeDeleted createMockAllowToBeDeleted() {
        AllowToBeDeleted allowToBeDeleted = new AllowToBeDeleted();
        allowToBeDeleted.allowToBeDeletedSPO = true;
        allowToBeDeleted.allowToBeDeletedODB = true;
        return allowToBeDeleted;
    }

    private SiteProperties createMockSiteProperties() {
        SiteProperties siteProps = new SiteProperties();
        siteProps.denyAddAndCustomizePages = 1; // 1 = Enabled
        return siteProps;
    }

    private Site createMockSite() {
        Site site = new Site();
        site.setWebUrl("https://contoso.sharepoint.com");
        site.setId("contoso.sharepoint.com,12345,67890");
        return site;
    }
}