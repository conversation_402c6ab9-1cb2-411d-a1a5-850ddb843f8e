package io.syrix.products.microsoft.teams.remediation;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.reports.model.opa.PolicyResult;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static java.util.stream.Collectors.toSet;

import static org.assertj.core.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("TeamsExternalMeetingChatRemediator Rego Policy Validation Tests")
class TeamsExternalMeetingChatRemediatorRegoPolicyTest {

    private ObjectMapper objectMapper;
    private static final String POLICY_ID = "MS.TEAMS.1.9v1";
    private static final String REGO_FILE_PATH = "src/main/resources/rego/TeamsConfig.rego";

    @BeforeEach
    void setUp() {
        objectMapper = new ObjectMapper();
        objectMapper.setPropertyNamingStrategy(PropertyNamingStrategies.UPPER_CAMEL_CASE);
    }

    @Nested
    @DisplayName("Rego Policy Rule Validation Tests")
    class RegoPolicyRuleValidationTests {

        @Test
        @DisplayName("Should validate Rego file contains MS.TEAMS.1.9v1 rule")
        void shouldValidateRegoFileContainsPolicy() throws IOException {
            // Given
            Path regoFilePath = Paths.get(REGO_FILE_PATH);
            
            // When
            String regoContent = Files.readString(regoFilePath);

            // Then
            assertThat(regoContent)
                    .as("Rego file should contain MeetingsAllowingExternalNonTrustedChat rule")
                    .contains("MeetingsAllowingExternalNonTrustedChat");
            
            assertThat(regoContent)
                    .as("Rego file should reference MS.TEAMS.1.9v1 policy")
                    .contains("MS.TEAMS.1.9v1");
            
            assertThat(regoContent)
                    .as("Rego file should check AllowExternalNonTrustedMeetingChat property")
                    .contains("AllowExternalNonTrustedMeetingChat");
        }

        @Test
        @DisplayName("Should validate Rego rule logic for compliant policies")
        void shouldValidateRegoRuleLogicForCompliantPolicies() throws IOException {
            // Given
            JsonNode compliantConfig = createCompliantTeamsConfiguration();
            
            // When - This would be validated by OPA engine in real scenario
            PolicyResult expectedResult = simulateOPAValidation(compliantConfig, true);

            // Then
            assertThat(expectedResult.getPolicyId()).isEqualTo(POLICY_ID);
            assertThat(expectedResult.isRequirementMet()).isTrue();
            assertThat(expectedResult.getReportDetails()).contains("compliant");
        }

        @Test
        @DisplayName("Should validate Rego rule logic for non-compliant policies")
        void shouldValidateRegoRuleLogicForNonCompliantPolicies() throws IOException {
            // Given
            JsonNode nonCompliantConfig = createNonCompliantTeamsConfiguration();
            
            // When - This would be validated by OPA engine in real scenario
            PolicyResult expectedResult = simulateOPAValidation(nonCompliantConfig, false);

            // Then
            assertThat(expectedResult.getPolicyId()).isEqualTo(POLICY_ID);
            assertThat(expectedResult.isRequirementMet()).isFalse();
            assertThat(expectedResult.getReportDetails()).contains("non-compliant");
        }

        @Test
        @DisplayName("Should validate Rego rule handles null values correctly")
        void shouldValidateRegoRuleHandlesNullValues() throws IOException {
            // Given
            JsonNode configWithNulls = createTeamsConfigurationWithNullValues();
            
            // When - Null values should be treated as non-compliant
            PolicyResult expectedResult = simulateOPAValidation(configWithNulls, false);

            // Then
            assertThat(expectedResult.getPolicyId()).isEqualTo(POLICY_ID);
            assertThat(expectedResult.isRequirementMet()).isFalse();
            assertThat(expectedResult.getReportDetails()).contains("null or missing");
        }
    }

    @Nested
    @DisplayName("Policy Configuration Data Tests")
    class PolicyConfigurationDataTests {

        @Test
        @DisplayName("Should validate policy data structure for OPA consumption")
        void shouldValidatePolicyDataStructureForOPA() {
            // Given
            JsonNode teamsConfig = createRealWorldTeamsConfiguration();
            
            // When
            JsonNode meetingPolicies = teamsConfig.get("meeting_policies");

            // Then
            assertThat(meetingPolicies).isNotNull();
            assertThat(meetingPolicies.isArray()).isTrue();
            
            meetingPolicies.forEach(policy -> {
                assertThat(policy.has("Identity")).isTrue();
                assertThat(policy.has("AllowExternalNonTrustedMeetingChat")).isTrue();
                
                String identity = policy.get("Identity").asText();
                assertThat(identity).isNotEmpty();
            });
        }

        @Test
        @DisplayName("Should validate OPA input format matches Teams configuration")
        void shouldValidateOPAInputFormatMatchesTeamsConfiguration() {
            // Given
            JsonNode teamsConfig = createTeamsConfigurationForOPA();
            
            // When
            ObjectNode opaInput = objectMapper.createObjectNode();
            opaInput.set("input", teamsConfig);

            // Then - Validate OPA input structure
            assertThat(opaInput.has("input")).isTrue();
            
            JsonNode inputData = opaInput.get("input");
            assertThat(inputData.has("meeting_policies")).isTrue();
            
            JsonNode policies = inputData.get("meeting_policies");
            assertThat(policies.isArray()).isTrue();
            assertThat(policies.size()).isGreaterThan(0);
        }

        @Test
        @DisplayName("Should validate policy filtering logic matches OPA expectations")
        void shouldValidatePolicyFilteringLogicMatchesOPA() {
            // Given
            JsonNode configWithVariousPolicies = createMixedPolicyConfiguration();
            
            // When - Filter policies as OPA would
            List<JsonNode> nonCompliantPolicies = extractNonCompliantPolicies(configWithVariousPolicies);
            List<JsonNode> tagPolicies = extractTagPolicies(configWithVariousPolicies);

            // Then
            assertThat(nonCompliantPolicies).isNotEmpty();
            assertThat(tagPolicies).isNotEmpty();
            
            // Verify Tag policies are properly identified
            tagPolicies.forEach(policy -> {
                String identity = policy.get("Identity").asText();
                assertThat(identity).startsWith("Tag:");
            });
            
            // Verify non-compliant policies have correct property values
            nonCompliantPolicies.forEach(policy -> {
                if (!policy.get("Identity").asText().startsWith("Tag:")) {
                    Boolean allowExternal = policy.get("AllowExternalNonTrustedMeetingChat").asBoolean();
                    assertThat(allowExternal).isTrue(); // Non-compliant value
                }
            });
        }
    }

    @Nested
    @DisplayName("OPA Integration Simulation Tests")
    class OPAIntegrationSimulationTests {

        @Test
        @DisplayName("Should simulate OPA policy evaluation for successful remediation")
        void shouldSimulateOPAEvaluationForSuccessfulRemediation() {
            // Given - Configuration before remediation
            JsonNode beforeRemediation = createNonCompliantTeamsConfiguration();
            JsonNode afterRemediation = createCompliantTeamsConfiguration();
            
            // When - Simulate OPA evaluation
            PolicyResult beforeResult = simulateOPAValidation(beforeRemediation, false);
            PolicyResult afterResult = simulateOPAValidation(afterRemediation, true);

            // Then
            assertThat(beforeResult.isRequirementMet()).isFalse();
            assertThat(afterResult.isRequirementMet()).isTrue();
            assertThat(beforeResult.getPolicyId()).isEqualTo(afterResult.getPolicyId());
        }

        @Test
        @DisplayName("Should simulate OPA evaluation with PowerShell error handling")
        void shouldSimulateOPAEvaluationWithPowerShellErrorHandling() {
            // Given
            JsonNode configWithPowerShellErrors = createConfigurationWithPowerShellErrors();
            
            // When
            PolicyResult result = simulateOPAValidationWithErrors(configWithPowerShellErrors);

            // Then
            assertThat(result.getPolicyId()).isEqualTo(POLICY_ID);
            assertThat(result.isRequirementMet()).isFalse();
            assertThat(result.getReportDetails()).contains("PowerShell error");
            assertThat(result.getCommandlets()).contains("Get-CsTeamsMeetingPolicy");
        }

        @Test
        @DisplayName("Should validate OPA production rule structure for MS.TEAMS.1.9v1")
        void shouldValidateOPATestCasesMatchRegoRule() throws IOException {
            // Given
            Path regoFilePath = Paths.get(REGO_FILE_PATH);
            String regoContent = Files.readString(regoFilePath);
            
            // When - Validate production Rego file structure (Syrix uses separate test files)
            boolean hasPolicyRule = regoContent.contains("MS.TEAMS.1.9v1") && 
                                  regoContent.contains("MeetingsAllowingExternalNonTrustedChat");

            // Then - Verify policy rule exists (test cases are in separate files)
            assertThat(hasPolicyRule)
                    .as("Rego file should contain production rule for MS.TEAMS.1.9v1")
                    .isTrue();
            
            // Validate core rule components exist
            assertThat(regoContent)
                    .as("Should have policy filtering logic")
                    .contains("AllowExternalNonTrustedMeetingChat");
            
            assertThat(regoContent)
                    .as("Should have Tag policy exclusion logic")
                    .contains("not startswith(Policy.Identity, \"Tag:\")");
        }
    }

    @Nested
    @DisplayName("Rego-Java Logic Consistency Tests")
    class RegoJavaConsistencyTests {
        
        @Test
        @DisplayName("Should have consistent logic for null and missing values")
        void shouldHaveConsistentLogicForNullAndMissingValues() throws IOException {
            // Given - Read actual Rego file
            String regoContent = Files.readString(Paths.get(REGO_FILE_PATH));
            
            // When - Check Rego logic matches Java logic
            // Java: !Boolean.FALSE.equals(policy.allowExternalNonTrustedMeetingChat)
            // Rego should use: Policy.AllowExternalNonTrustedMeetingChat != false
            boolean hasCorrectLogic = regoContent.contains("AllowExternalNonTrustedMeetingChat != false");
            boolean hasIncorrectLogic = regoContent.contains("AllowExternalNonTrustedMeetingChat == true");
            
            // Then - Rego should use same logic as Java (treat null/missing as non-compliant)
            assertThat(hasCorrectLogic)
                    .as("Rego should use '!= false' to match Java logic for null/missing values")
                    .isTrue();
                    
            assertThat(hasIncorrectLogic)
                    .as("Rego should not use '== true' which treats null/missing as compliant")
                    .isFalse();
        }

        @Test 
        @DisplayName("Should validate Rego and Java filter same policies")
        void shouldValidateRegoAndJavaFilterSamePolicies() {
            // Given - Test data with various AllowExternalNonTrustedMeetingChat values
            JsonNode testConfig = createComprehensiveTestConfiguration();
            
            // When - Apply both Java and simulated Rego filtering
            List<JsonNode> javaFilteredPolicies = simulateJavaFiltering(testConfig);
            List<JsonNode> regoFilteredPolicies = simulateRegoFiltering(testConfig);
            
            // Then - Both should identify the same non-compliant policies
            assertThat(javaFilteredPolicies.size())
                    .as("Java and Rego should filter same number of non-compliant policies")
                    .isEqualTo(regoFilteredPolicies.size());
                    
            // Verify same policy identities are flagged by both
            Set<String> javaIdentities = javaFilteredPolicies.stream()
                    .map(p -> p.get("Identity").asText())
                    .collect(toSet());
            Set<String> regoIdentities = regoFilteredPolicies.stream()
                    .map(p -> p.get("Identity").asText())  
                    .collect(toSet());
                    
            assertThat(javaIdentities)
                    .as("Java and Rego should flag identical policy identities")
                    .isEqualTo(regoIdentities);
        }
        
        private JsonNode createComprehensiveTestConfiguration() {
            ArrayNode policies = objectMapper.createArrayNode();
            
            // Test all edge cases that caused the original inconsistency
            policies.add(objectMapper.createObjectNode()
                    .put("Identity", "ExplicitTrue")
                    .put("AllowExternalNonTrustedMeetingChat", true));
                    
            policies.add(objectMapper.createObjectNode()
                    .put("Identity", "ExplicitFalse") 
                    .put("AllowExternalNonTrustedMeetingChat", false));
                    
            policies.add(objectMapper.createObjectNode()
                    .put("Identity", "MissingProperty"));
                    // No AllowExternalNonTrustedMeetingChat property
                    
            policies.add(objectMapper.createObjectNode()
                    .put("Identity", "NullValue")
                    .putNull("AllowExternalNonTrustedMeetingChat"));
                    
            policies.add(objectMapper.createObjectNode()
                    .put("Identity", "Tag:ShouldBeIgnored")
                    .put("AllowExternalNonTrustedMeetingChat", true));
            
            ObjectNode config = objectMapper.createObjectNode();
            config.set("meeting_policies", policies);
            return config;
        }
        
        private List<JsonNode> simulateJavaFiltering(JsonNode config) {
            // Simulate: !Boolean.FALSE.equals(policy.allowExternalNonTrustedMeetingChat) && !policy.identity.startsWith("Tag:")
            List<JsonNode> filtered = new ArrayList<>();
            JsonNode policies = config.get("meeting_policies");
            
            if (policies != null && policies.isArray()) {
                for (JsonNode policy : policies) {
                    JsonNode identityNode = policy.get("Identity");
                    JsonNode allowExternalNode = policy.get("AllowExternalNonTrustedMeetingChat");
                    
                    if (identityNode != null && !identityNode.asText().startsWith("Tag:")) {
                        // Java logic: !Boolean.FALSE.equals(...) means null/missing/true are non-compliant
                        if (allowExternalNode == null || allowExternalNode.isNull() || allowExternalNode.asBoolean() != false) {
                            filtered.add(policy);
                        }
                    }
                }
            }
            return filtered;
        }
        
        private List<JsonNode> simulateRegoFiltering(JsonNode config) {
            // Simulate: Policy.AllowExternalNonTrustedMeetingChat != false && not startswith(Policy.Identity, "Tag:")
            List<JsonNode> filtered = new ArrayList<>();
            JsonNode policies = config.get("meeting_policies");
            
            if (policies != null && policies.isArray()) {
                for (JsonNode policy : policies) {
                    JsonNode identityNode = policy.get("Identity");
                    JsonNode allowExternalNode = policy.get("AllowExternalNonTrustedMeetingChat");
                    
                    if (identityNode != null && !identityNode.asText().startsWith("Tag:")) {
                        // Rego logic: != false means null/missing/true are non-compliant (same as Java)
                        if (allowExternalNode == null || allowExternalNode.isNull() || allowExternalNode.asBoolean() != false) {
                            filtered.add(policy);
                        }
                    }
                }
            }
            return filtered;
        }
    }

    @Nested
    @DisplayName("Policy Result Format Tests")
    class PolicyResultFormatTests {

        @Test
        @DisplayName("Should validate PolicyResult format matches expected OPA output")
        void shouldValidatePolicyResultFormatMatchesOPA() {
            // Given
            PolicyResult expectedResult = createExpectedPolicyResult();
            
            // When
            JsonNode resultAsJson = objectMapper.valueToTree(expectedResult);

            // Then
            assertThat(resultAsJson.has("PolicyId")).isTrue();
            assertThat(resultAsJson.has("RequirementMet")).isTrue();
            assertThat(resultAsJson.has("ReportDetails")).isTrue();
            assertThat(resultAsJson.has("Criticality")).isTrue();
            assertThat(resultAsJson.has("ActualValue")).isTrue();
            assertThat(resultAsJson.has("Commandlet")).isTrue();
            
            assertThat(resultAsJson.get("PolicyId").asText()).isEqualTo(POLICY_ID);
        }

        @Test
        @DisplayName("Should validate ActualValue format for Teams meeting policies")
        void shouldValidateActualValueFormatForTeamsPolicies() {
            // Given
            Map<String, Object> actualValue = Map.of(
                    "non_compliant_policies", List.of("Global", "RestrictivePolicy"),
                    "compliant_policies", List.of("Tag:ExecutivePolicy"),
                    "total_policies", 3,
                    "external_chat_enabled_count", 2
            );
            
            // When
            PolicyResult result = new PolicyResult();
            result.setPolicyId(POLICY_ID);
            result.setActualValue(actualValue);
            result.setRequirementMet(false);

            // Then
            assertThat(result.getActualValue()).isInstanceOf(Map.class);
            
            @SuppressWarnings("unchecked")
            Map<String, Object> actualValueMap = (Map<String, Object>) result.getActualValue();
            assertThat(actualValueMap).containsKeys(
                    "non_compliant_policies", 
                    "compliant_policies",
                    "total_policies",
                    "external_chat_enabled_count"
            );
        }

        @Test
        @DisplayName("Should validate Commandlet list format for Teams policies")
        void shouldValidateCommandletListFormatForTeamsPolicies() {
            // Given
            List<String> expectedCommandlets = List.of(
                    "Get-CsTeamsMeetingPolicy",
                    "Set-CsTeamsMeetingPolicy"
            );
            
            // When
            PolicyResult result = new PolicyResult();
            result.setPolicyId(POLICY_ID);
            result.setCommandlets(expectedCommandlets);

            // Then
            assertThat(result.getCommandlets()).isNotNull();
            assertThat(result.getCommandlets()).hasSize(2);
            assertThat(result.getCommandlets()).containsExactlyElementsOf(expectedCommandlets);
        }
    }

    // Helper methods for creating test data
    private JsonNode createCompliantTeamsConfiguration() {
        ArrayNode policies = objectMapper.createArrayNode();
        policies.add(objectMapper.createObjectNode()
                .put("Identity", "Global")
                .put("AllowExternalNonTrustedMeetingChat", false));
        policies.add(objectMapper.createObjectNode()
                .put("Identity", "RestrictivePolicy")
                .put("AllowExternalNonTrustedMeetingChat", false));
        
        ObjectNode config = objectMapper.createObjectNode();
        config.set("meeting_policies", policies);
        return config;
    }

    private JsonNode createNonCompliantTeamsConfiguration() {
        ArrayNode policies = objectMapper.createArrayNode();
        policies.add(objectMapper.createObjectNode()
                .put("Identity", "Global")
                .put("AllowExternalNonTrustedMeetingChat", true));
        policies.add(objectMapper.createObjectNode()
                .put("Identity", "RestrictivePolicy")
                .put("AllowExternalNonTrustedMeetingChat", true));
        
        ObjectNode config = objectMapper.createObjectNode();
        config.set("meeting_policies", policies);
        return config;
    }

    private JsonNode createTeamsConfigurationWithNullValues() {
        ArrayNode policies = objectMapper.createArrayNode();
        policies.add(objectMapper.createObjectNode()
                .put("Identity", "Global"));
        // Missing AllowExternalNonTrustedMeetingChat property
        
        ObjectNode config = objectMapper.createObjectNode();
        config.set("meeting_policies", policies);
        return config;
    }

    private JsonNode createRealWorldTeamsConfiguration() {
        ArrayNode policies = objectMapper.createArrayNode();
        
        policies.add(objectMapper.createObjectNode()
                .put("Identity", "Global")
                .put("AllowExternalNonTrustedMeetingChat", true)
                .put("AllowAnonymousUsersToJoinMeeting", false));
        
        policies.add(objectMapper.createObjectNode()
                .put("Identity", "Tag:ExecutivePolicy")
                .put("AllowExternalNonTrustedMeetingChat", false));
        
        policies.add(objectMapper.createObjectNode()
                .put("Identity", "RestrictivePolicy")
                .put("AllowExternalNonTrustedMeetingChat", false));
        
        ObjectNode config = objectMapper.createObjectNode();
        config.set("meeting_policies", policies);
        return config;
    }

    private JsonNode createTeamsConfigurationForOPA() {
        return createRealWorldTeamsConfiguration();
    }

    private JsonNode createMixedPolicyConfiguration() {
        ArrayNode policies = objectMapper.createArrayNode();
        
        // Non-compliant regular policies
        policies.add(objectMapper.createObjectNode()
                .put("Identity", "Global")
                .put("AllowExternalNonTrustedMeetingChat", true));
        
        policies.add(objectMapper.createObjectNode()
                .put("Identity", "DepartmentPolicy")
                .put("AllowExternalNonTrustedMeetingChat", true));
        
        // Compliant regular policy
        policies.add(objectMapper.createObjectNode()
                .put("Identity", "SecurePolicy")
                .put("AllowExternalNonTrustedMeetingChat", false));
        
        // Tag policies (should be filtered out)
        policies.add(objectMapper.createObjectNode()
                .put("Identity", "Tag:ExecutivePolicy")
                .put("AllowExternalNonTrustedMeetingChat", false));
        
        policies.add(objectMapper.createObjectNode()
                .put("Identity", "Tag:SpecialPolicy")
                .put("AllowExternalNonTrustedMeetingChat", true));
        
        ObjectNode config = objectMapper.createObjectNode();
        config.set("meeting_policies", policies);
        return config;
    }

    private JsonNode createConfigurationWithPowerShellErrors() {
        // This simulates a configuration that would cause PowerShell errors
        ArrayNode policies = objectMapper.createArrayNode();
        policies.add(objectMapper.createObjectNode()
                .put("Identity", "InvalidPolicy")
                .put("AllowExternalNonTrustedMeetingChat", true)
                .put("Error", "PowerShell execution failed"));
        
        ObjectNode config = objectMapper.createObjectNode();
        config.set("meeting_policies", policies);
        return config;
    }

    // Simulation methods (in real implementation, these would use OPA engine)
    private PolicyResult simulateOPAValidation(JsonNode config, boolean isCompliant) {
        PolicyResult result = new PolicyResult();
        result.setPolicyId(POLICY_ID);
        result.setRequirementMet(isCompliant);
        result.setCriticality("Shall");
        result.setCommandlets(List.of("Get-CsTeamsMeetingPolicy"));
        
        if (isCompliant) {
            result.setReportDetails("All meeting policies are compliant - external non-trusted meeting chat is disabled");
            result.setActualValue(Map.of("compliant_policies", 2, "non_compliant_policies", 0));
        } else {
            // Check if config has null/missing values
            boolean hasNullValues = hasNullOrMissingValues(config);
            if (hasNullValues) {
                result.setReportDetails("Found meeting policies with null or missing AllowExternalNonTrustedMeetingChat values");
                result.setActualValue(Map.of("policies_with_null_values", 1, "compliant_policies", 0));
            } else {
                result.setReportDetails("Found non-compliant meeting policies with external chat enabled");
                result.setActualValue(Map.of("compliant_policies", 0, "non_compliant_policies", 2));
            }
        }
        
        return result;
    }
    
    private boolean hasNullOrMissingValues(JsonNode config) {
        if (config.has("meeting_policies")) {
            JsonNode policies = config.get("meeting_policies");
            for (JsonNode policy : policies) {
                if (!policy.has("AllowExternalNonTrustedMeetingChat") || 
                    policy.get("AllowExternalNonTrustedMeetingChat").isNull()) {
                    return true;
                }
            }
        }
        return false;
    }

    private PolicyResult simulateOPAValidationWithErrors(JsonNode config) {
        PolicyResult result = new PolicyResult();
        result.setPolicyId(POLICY_ID);
        result.setRequirementMet(false);
        result.setCriticality("Shall");
        result.setCommandlets(List.of("Get-CsTeamsMeetingPolicy"));
        result.setReportDetails("PowerShell error occurred while retrieving meeting policies");
        result.setActualValue(Map.of("error", "PowerShell execution failed"));
        
        return result;
    }

    private List<JsonNode> extractNonCompliantPolicies(JsonNode config) {
        List<JsonNode> nonCompliant = new ArrayList<>();
        JsonNode policies = config.get("meeting_policies");
        
        if (policies != null && policies.isArray()) {
            for (JsonNode policy : policies) {
                JsonNode allowExternalNode = policy.get("AllowExternalNonTrustedMeetingChat");
                if (allowExternalNode != null && allowExternalNode.asBoolean()) {
                    nonCompliant.add(policy);
                }
            }
        }
        return nonCompliant;
    }

    private List<JsonNode> extractTagPolicies(JsonNode config) {
        List<JsonNode> tagPolicies = new ArrayList<>();
        JsonNode policies = config.get("meeting_policies");
        
        if (policies != null && policies.isArray()) {
            for (JsonNode policy : policies) {
                JsonNode identityNode = policy.get("Identity");
                if (identityNode != null && identityNode.asText().startsWith("Tag:")) {
                    tagPolicies.add(policy);
                }
            }
        }
        return tagPolicies;
    }

    private PolicyResult createExpectedPolicyResult() {
        PolicyResult result = new PolicyResult();
        result.setPolicyId(POLICY_ID);
        result.setRequirementMet(false);
        result.setCriticality("Shall");
        result.setReportDetails("2 meeting policy(s) found that allow external non-trusted meeting chat");
        result.setCommandlets(List.of("Get-CsTeamsMeetingPolicy"));
        result.setActualValue(Map.of(
                "non_compliant_policies", List.of("Global", "RestrictivePolicy"),
                "total_policies", 2
        ));
        
        return result;
    }
}