package io.syrix.products.microsoft.teams.remediation;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.common.utils.TestUtils;
import io.syrix.datamodel.task.remediation.teams.TeamsRemediationConfig;
import io.syrix.products.microsoft.base.PolicyChangeResult;
import io.syrix.products.microsoft.base.RemediationResult;
import io.syrix.products.microsoft.teams.model.TeamsMeetingPolicy;
import io.syrix.protocols.client.PowerShellTeamsClient;
import io.syrix.protocols.client.teams.powershell.command.CsTeamsCommand;
import io.syrix.protocols.client.teams.powershell.command.types.MeetingPolicy;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

import static org.assertj.core.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("TeamsExternalMeetingChatRemediator Integration Tests")
class TeamsExternalMeetingChatRemediatorIntegrationTest {

    @Mock
    private PowerShellTeamsClient mockClient;

    @Mock
    private TeamsRemediationConfig remediationConfig;

    @Captor
    private ArgumentCaptor<CsTeamsCommand<MeetingPolicy>> commandCaptor;

    private TeamsExternalMeetingChatRemediator remediator;
    private ObjectMapper objectMapper;
    private ObjectNode configNode;

    @BeforeEach
    void setUp() {
        objectMapper = new ObjectMapper();
        objectMapper.setPropertyNamingStrategy(PropertyNamingStrategies.UPPER_CAMEL_CASE);
        
        configNode = objectMapper.createObjectNode();
        remediator = new TeamsExternalMeetingChatRemediator(mockClient, configNode, remediationConfig);
    }

    @Nested
    @DisplayName("PowerShell Command Integration Tests")
    class PowerShellCommandIntegrationTests {

        @Test
        @DisplayName("Should execute PowerShell commands with correct parameters")
        void shouldExecutePowerShellCommandsWithCorrectParameters() throws Exception {
            // Given
            setupMockConfigurationWithNonCompliantPolicies();
            when(mockClient.execute(any(CsTeamsCommand.class)))
                    .thenReturn(CompletableFuture.completedFuture(Collections.emptyList()));

            // When
            PolicyChangeResult result = remediator.remediate_().get(5, TimeUnit.SECONDS);

            // Then
            assertThat(result.getResult()).isEqualTo(RemediationResult.SUCCESS);
            
            verify(mockClient, times(2)).execute(commandCaptor.capture());
            List<CsTeamsCommand<MeetingPolicy>> capturedCommands = commandCaptor.getAllValues();
            
            assertThat(capturedCommands).hasSize(2);
            capturedCommands.forEach(command -> {
                assertThat(command.getName()).isEqualTo("Set-CsTeamsMeetingPolicy");
            });
        }

        @Test
        @DisplayName("Should handle PowerShell execution timeout")
        void shouldHandlePowerShellExecutionTimeout() throws Exception {
            // Given
            setupMockConfigurationWithNonCompliantPolicies();
            CompletableFuture<List<MeetingPolicy>> timeoutFuture = new CompletableFuture<>();
            // Don't complete the future to simulate timeout
            when(mockClient.execute(any(CsTeamsCommand.class)))
                    .thenReturn(timeoutFuture);

            // When
            CompletableFuture<PolicyChangeResult> resultFuture = remediator.remediate_();

            // Then - Should handle gracefully without hanging
            assertThatNoException().isThrownBy(() -> {
                // Wait a short time to ensure it doesn't complete immediately
                Thread.sleep(100);
                assertThat(resultFuture.isDone()).isFalse();
            });
        }

        @Test
        @DisplayName("Should retry PowerShell commands on failure")
        void shouldRetryPowerShellCommandsOnFailure() throws Exception {
            // Given
            setupMockConfigurationWithNonCompliantPolicies();
            when(mockClient.execute(any(CsTeamsCommand.class)))
                    .thenReturn(CompletableFuture.failedFuture(
                            new RuntimeException("PowerShell execution failed")));

            // When
            PolicyChangeResult result = remediator.remediate_().get(5, TimeUnit.SECONDS);

            // Then
            assertThat(result.getResult()).isEqualTo(RemediationResult.FAILED);
            verify(mockClient, atLeast(2)).execute(any(CsTeamsCommand.class));
        }

        @Test
        @DisplayName("Should execute commands concurrently for multiple policies")
        void shouldExecuteCommandsConcurrentlyForMultiplePolicies() throws Exception {
            // Given
            setupMockConfigurationWithMultipleNonCompliantPolicies();
            when(mockClient.execute(any(CsTeamsCommand.class)))
                    .thenReturn(CompletableFuture.completedFuture(Collections.emptyList()));

            long startTime = System.currentTimeMillis();

            // When
            PolicyChangeResult result = remediator.remediate_().get(10, TimeUnit.SECONDS);

            // Then
            long executionTime = System.currentTimeMillis() - startTime;
            assertThat(result.getResult()).isEqualTo(RemediationResult.SUCCESS);
            
            // Should complete quickly due to concurrent execution
            assertThat(executionTime).isLessThan(2000);
            verify(mockClient, times(3)).execute(any(CsTeamsCommand.class));
        }
    }

    @Nested
    @DisplayName("Configuration Integration Tests")
    class ConfigurationIntegrationTests {

        @Test
        @DisplayName("Should load real Teams meeting policy data")
        void shouldLoadRealTeamsMeetingPolicyData() throws Exception {
            // Given
            JsonNode realPolicyData = TestUtils.loadTestData("teams/meeting-policies.json");
            setupConfigNodeWithRealData(realPolicyData);
            
            // When - Test configuration loading (no PowerShell execution needed)
            PolicyChangeResult result = remediator.remediate_().get(5, TimeUnit.SECONDS);

            // Then
            assertThat(result).isNotNull();
            assertThat(result.getPolicyId()).isEqualTo("MS.TEAMS.1.9v1");
        }

        @Test
        @DisplayName("Should handle malformed configuration data")
        void shouldHandleMalformedConfigurationData() throws Exception {
            // Given
            ObjectNode malformedConfig = objectMapper.createObjectNode();
            malformedConfig.put("invalid", "data");
            
            TeamsExternalMeetingChatRemediator malformedRemediator = 
                    new TeamsExternalMeetingChatRemediator(mockClient, malformedConfig, remediationConfig);

            // When
            PolicyChangeResult result = malformedRemediator.remediate_().get(5, TimeUnit.SECONDS);

            // Then
            assertThat(result.getResult()).isEqualTo(RemediationResult.FAILED);
            assertThat(result.getDesc()).contains("No policies found in configuration");
        }

        @Test
        @DisplayName("Should validate configuration keys and structure")
        void shouldValidateConfigurationKeysAndStructure() throws Exception {
            // Given
            ObjectNode configWithCorrectKeys = objectMapper.createObjectNode();
            configWithCorrectKeys.set("meeting_policies", createValidMeetingPoliciesArray());
            
            TeamsExternalMeetingChatRemediator validRemediator = 
                    new TeamsExternalMeetingChatRemediator(mockClient, configWithCorrectKeys, remediationConfig);
                    
            when(mockClient.execute(any(CsTeamsCommand.class)))
                    .thenReturn(CompletableFuture.completedFuture(Collections.emptyList()));

            // When
            PolicyChangeResult result = validRemediator.remediate_().get(5, TimeUnit.SECONDS);

            // Then
            assertThat(result.getResult()).isEqualTo(RemediationResult.SUCCESS);
            assertThat(result.getChanges()).isNotEmpty();
        }
    }

    @Nested
    @DisplayName("End-to-End Workflow Tests")
    class EndToEndWorkflowTests {

        @Test
        @DisplayName("Should complete full remediation workflow")
        void shouldCompleteFullRemediationWorkflow() throws Exception {
            // Given - Real-world scenario setup
            setupRealWorldScenarioConfiguration();
            when(mockClient.execute(any(CsTeamsCommand.class)))
                    .thenReturn(CompletableFuture.completedFuture(Collections.emptyList()));

            // When - Execute full workflow
            CompletableFuture<JsonNode> jsonResult = remediator.remediate();
            PolicyChangeResult policyResult = remediator.remediate_().get(5, TimeUnit.SECONDS);

            // Then - Validate complete workflow
            JsonNode jsonResultValue = jsonResult.get(5, TimeUnit.SECONDS);
            assertThat(jsonResultValue).isNotNull();
            
            assertThat(policyResult.getResult()).isEqualTo(RemediationResult.SUCCESS);
            assertThat(policyResult.getPolicyId()).isEqualTo("MS.TEAMS.1.9v1");
            assertThat(policyResult.getChanges()).isNotEmpty();
            
            // Validate PowerShell commands were executed
            verify(mockClient, atLeast(1)).execute(any(CsTeamsCommand.class));
        }

        @Test
        @DisplayName("Should handle complete workflow with rollback")
        void shouldHandleCompleteWorkflowWithRollback() throws Exception {
            // Given - Setup successful remediation first
            setupMockConfigurationWithNonCompliantPolicies();
            when(mockClient.execute(any(CsTeamsCommand.class)))
                    .thenReturn(CompletableFuture.completedFuture(Collections.emptyList()));

            // When - Execute remediation and rollback
            PolicyChangeResult remediationResult = remediator.remediate_().get(5, TimeUnit.SECONDS);
            PolicyChangeResult rollbackResult = remediator.rollback(remediationResult).get(5, TimeUnit.SECONDS);

            // Then - Validate both operations
            assertThat(remediationResult.getResult()).isEqualTo(RemediationResult.SUCCESS);
            assertThat(rollbackResult.getResult()).isEqualTo(RemediationResult.SUCCESS);
            
            // Verify both remediation and rollback commands were executed
            verify(mockClient, atLeast(4)).execute(any(CsTeamsCommand.class));
        }

        @Test
        @DisplayName("Should validate external meeting chat property changes")
        void shouldValidateExternalMeetingChatPropertyChanges() throws Exception {
            // Given
            setupMockConfigurationWithSpecificPropertyValues();
            when(mockClient.execute(any(CsTeamsCommand.class)))
                    .thenReturn(CompletableFuture.completedFuture(Collections.emptyList()));

            // When
            PolicyChangeResult result = remediator.remediate_().get(5, TimeUnit.SECONDS);

            // Then - Validate specific property changes
            assertThat(result.getChanges()).allSatisfy(change -> {
                assertThat(change.getParameter()).contains("allowExternalNonTrustedMeetingChat");
                assertThat(change.getNewValue()).isEqualTo(false);
                assertThat(change.getPrevValue()).isEqualTo(true);
            });

            // Verify PowerShell commands contain correct property values
            verify(mockClient, atLeastOnce()).execute(commandCaptor.capture());
            commandCaptor.getAllValues().forEach(command -> {
                // Would need access to command parameters to validate
                assertThat(command.getName()).isEqualTo("Set-CsTeamsMeetingPolicy");
            });
        }
    }

    @Nested
    @DisplayName("Performance and Load Tests")
    class PerformanceAndLoadTests {

        @Test
        @DisplayName("Should handle large number of policies efficiently")
        void shouldHandleLargeNumberOfPoliciesEfficiently() throws Exception {
            // Given - Create configuration with many policies
            setupConfigurationWithManyPolicies(50);
            when(mockClient.execute(any(CsTeamsCommand.class)))
                    .thenReturn(CompletableFuture.completedFuture(Collections.emptyList()));

            long startTime = System.currentTimeMillis();

            // When
            PolicyChangeResult result = remediator.remediate_().get(30, TimeUnit.SECONDS);

            // Then
            long executionTime = System.currentTimeMillis() - startTime;
            assertThat(result.getResult()).isEqualTo(RemediationResult.SUCCESS);
            assertThat(executionTime).isLessThan(10000); // Should complete within 10 seconds
            
            verify(mockClient, times(50)).execute(any(CsTeamsCommand.class));
        }

        @Test
        @DisplayName("Should handle concurrent remediation requests")
        void shouldHandleConcurrentRemediationRequests() throws Exception {
            // Given
            setupMockConfigurationWithNonCompliantPolicies();
            when(mockClient.execute(any(CsTeamsCommand.class)))
                    .thenReturn(CompletableFuture.completedFuture(Collections.emptyList()));

            // When - Execute multiple concurrent requests
            CompletableFuture<PolicyChangeResult> future1 = remediator.remediate_();
            CompletableFuture<PolicyChangeResult> future2 = remediator.remediate_();
            CompletableFuture<PolicyChangeResult> future3 = remediator.remediate_();

            // Then - All should complete successfully
            PolicyChangeResult result1 = future1.get(10, TimeUnit.SECONDS);
            PolicyChangeResult result2 = future2.get(10, TimeUnit.SECONDS);
            PolicyChangeResult result3 = future3.get(10, TimeUnit.SECONDS);

            assertThat(result1.getResult()).isEqualTo(RemediationResult.SUCCESS);
            assertThat(result2.getResult()).isEqualTo(RemediationResult.SUCCESS);
            assertThat(result3.getResult()).isEqualTo(RemediationResult.SUCCESS);
        }
    }

    // Helper methods for test setup
    private void setupMockConfigurationWithNonCompliantPolicies() {
        ObjectNode meetingPoliciesConfig = objectMapper.createObjectNode();
        meetingPoliciesConfig.set("meeting_policies", createNonCompliantMeetingPoliciesArray());
        
        TeamsExternalMeetingChatRemediator testRemediator = 
                new TeamsExternalMeetingChatRemediator(mockClient, meetingPoliciesConfig, remediationConfig);
        this.remediator = testRemediator;
    }

    private void setupMockConfigurationWithMultipleNonCompliantPolicies() {
        ObjectNode meetingPoliciesConfig = objectMapper.createObjectNode();
        meetingPoliciesConfig.set("meeting_policies", createMultipleNonCompliantPoliciesArray());
        
        TeamsExternalMeetingChatRemediator testRemediator = 
                new TeamsExternalMeetingChatRemediator(mockClient, meetingPoliciesConfig, remediationConfig);
        this.remediator = testRemediator;
    }

    private void setupMockConfigurationWithSpecificPropertyValues() {
        ObjectNode meetingPoliciesConfig = objectMapper.createObjectNode();
        meetingPoliciesConfig.set("meeting_policies", createSpecificPropertyValuesPoliciesArray());
        
        TeamsExternalMeetingChatRemediator testRemediator = 
                new TeamsExternalMeetingChatRemediator(mockClient, meetingPoliciesConfig, remediationConfig);
        this.remediator = testRemediator;
    }

    private void setupConfigurationWithManyPolicies(int count) {
        ObjectNode meetingPoliciesConfig = objectMapper.createObjectNode();
        meetingPoliciesConfig.set("meeting_policies", createManyPoliciesArray(count));
        
        TeamsExternalMeetingChatRemediator testRemediator = 
                new TeamsExternalMeetingChatRemediator(mockClient, meetingPoliciesConfig, remediationConfig);
        this.remediator = testRemediator;
    }

    private void setupRealWorldScenarioConfiguration() {
        ObjectNode realWorldConfig = objectMapper.createObjectNode();
        realWorldConfig.set("meeting_policies", createRealWorldScenarioArray());
        
        TeamsExternalMeetingChatRemediator testRemediator = 
                new TeamsExternalMeetingChatRemediator(mockClient, realWorldConfig, remediationConfig);
        this.remediator = testRemediator;
    }

    private void setupConfigNodeWithRealData(JsonNode realData) {
        configNode.set("meeting_policies", realData.get("value"));
        TeamsExternalMeetingChatRemediator testRemediator = 
                new TeamsExternalMeetingChatRemediator(mockClient, configNode, remediationConfig);
        this.remediator = testRemediator;
    }

    private JsonNode createNonCompliantMeetingPoliciesArray() {
        return objectMapper.createArrayNode()
                .add(objectMapper.createObjectNode()
                        .put("Identity", "Global")
                        .put("AllowExternalNonTrustedMeetingChat", true))
                .add(objectMapper.createObjectNode()
                        .put("Identity", "RestrictivePolicy")
                        .put("AllowExternalNonTrustedMeetingChat", true));
    }

    private JsonNode createMultipleNonCompliantPoliciesArray() {
        return objectMapper.createArrayNode()
                .add(objectMapper.createObjectNode()
                        .put("Identity", "Policy1")
                        .put("AllowExternalNonTrustedMeetingChat", true))
                .add(objectMapper.createObjectNode()
                        .put("Identity", "Policy2")
                        .put("AllowExternalNonTrustedMeetingChat", true))
                .add(objectMapper.createObjectNode()
                        .put("Identity", "Policy3")
                        .put("AllowExternalNonTrustedMeetingChat", true));
    }

    private JsonNode createSpecificPropertyValuesPoliciesArray() {
        return objectMapper.createArrayNode()
                .add(objectMapper.createObjectNode()
                        .put("Identity", "TestPolicy")
                        .put("AllowExternalNonTrustedMeetingChat", true)
                        .put("AllowAnonymousUsersToJoinMeeting", false)
                        .put("AllowMeetNow", true));
    }

    private JsonNode createValidMeetingPoliciesArray() {
        return objectMapper.createArrayNode()
                .add(objectMapper.createObjectNode()
                        .put("Identity", "CompliantPolicy")
                        .put("AllowExternalNonTrustedMeetingChat", true)); // Will be remediated
    }

    private JsonNode createManyPoliciesArray(int count) {
        var arrayNode = objectMapper.createArrayNode();
        for (int i = 1; i <= count; i++) {
            arrayNode.add(objectMapper.createObjectNode()
                    .put("Identity", "Policy" + i)
                    .put("AllowExternalNonTrustedMeetingChat", true));
        }
        return arrayNode;
    }

    private JsonNode createRealWorldScenarioArray() {
        return objectMapper.createArrayNode()
                .add(objectMapper.createObjectNode()
                        .put("Identity", "Global")
                        .put("AllowExternalNonTrustedMeetingChat", true)
                        .put("AllowAnonymousUsersToJoinMeeting", false)
                        .put("AllowMeetNow", true))
                .add(objectMapper.createObjectNode()
                        .put("Identity", "Tag:ExecutivesPolicy")
                        .put("AllowExternalNonTrustedMeetingChat", false)) // Should be skipped
                .add(objectMapper.createObjectNode()
                        .put("Identity", "RestrictivePolicy")
                        .put("AllowExternalNonTrustedMeetingChat", false)) // Already compliant
                .add(objectMapper.createObjectNode()
                        .put("Identity", "DepartmentPolicy")
                        .put("AllowExternalNonTrustedMeetingChat", true)); // Needs remediation
    }
}