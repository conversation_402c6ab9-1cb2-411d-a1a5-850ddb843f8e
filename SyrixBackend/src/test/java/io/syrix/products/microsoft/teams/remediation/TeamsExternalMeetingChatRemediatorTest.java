package io.syrix.products.microsoft.teams.remediation;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.products.microsoft.base.ParameterChangeResult;
import io.syrix.products.microsoft.base.ParameterChangeStatus;
import io.syrix.products.microsoft.base.PolicyChangeResult;
import io.syrix.products.microsoft.base.RemediationResult;
import io.syrix.datamodel.task.remediation.teams.TeamsRemediationConfig;
import io.syrix.protocols.client.PowerShellTeamsClient;
import io.syrix.protocols.client.teams.powershell.command.CsTeamsCommand;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * Comprehensive unit tests for TeamsExternalMeetingChatRemediator class.
 *
 * Tests cover the complete remediation lifecycle including:
 * - Detection of current external meeting chat settings
 * - Remediation when allowExternalNonTrustedMeetingChat is enabled
 * - Requirement already met scenarios
 * - Error handling for configuration issues
 * - Rollback functionality for policy reversals
 * - Tag policy filtering
 * - CompletableFuture async patterns
 * - ParameterChangeResult tracking
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("Teams External Meeting Chat Remediator Tests")
class TeamsExternalMeetingChatRemediatorTest {

    @Mock
    private PowerShellTeamsClient mockClient;

    @Mock
    private TeamsRemediationConfig mockRemediationConfig;

    @Captor
    private ArgumentCaptor<CsTeamsCommand> commandCaptor;

    private ObjectMapper objectMapper;
    private ObjectNode configNode;
    private TeamsExternalMeetingChatRemediator remediator;

    @BeforeEach
    void setUp() {
        objectMapper = new ObjectMapper();
        configNode = objectMapper.createObjectNode();

        // Create test meeting policies JSON with proper casing to match UPPER_CAMEL_CASE naming strategy
        var meetingPoliciesArray = configNode.putArray("meeting_policies");

        // Non-compliant policy - allowExternalNonTrustedMeetingChat = true
        meetingPoliciesArray.addObject()
            .put("Identity", "Global")
            .put("AllowExternalNonTrustedMeetingChat", true);

        // Another non-compliant policy
        meetingPoliciesArray.addObject()
            .put("Identity", "TestPolicy")
            .put("AllowExternalNonTrustedMeetingChat", true);

        // Tag policy should be filtered out
        meetingPoliciesArray.addObject()
            .put("Identity", "Tag:SomePolicy")
            .put("AllowExternalNonTrustedMeetingChat", true);

        remediator = new TeamsExternalMeetingChatRemediator(mockClient, configNode, mockRemediationConfig);
    }

    @Nested
    @DisplayName("Policy ID and Constructor Tests")
    class BasicFunctionalityTests {

        @Test
        @DisplayName("Should return correct policy ID")
        void testGetPolicyId() {
            assertEquals("MS.TEAMS.1.9v1", remediator.getPolicyId());
        }

        @Test
        @DisplayName("Should construct with client only")
        void testConstructorWithClientOnly() {
            TeamsExternalMeetingChatRemediator simpleRemediator = new TeamsExternalMeetingChatRemediator(mockClient);
            assertEquals("MS.TEAMS.1.9v1", simpleRemediator.getPolicyId());
        }
    }

    @Nested
    @DisplayName("External Meeting Chat Detection Tests")
    class ExternalMeetingChatDetectionTests {

        @Test
        @DisplayName("Should detect policies with external meeting chat enabled")
        void shouldDetectExternalMeetingChatEnabled() throws Exception {
            // Mock successful PowerShell execution - return empty list of MeetingPolicy
            when(mockClient.execute(any()))
                .thenReturn(CompletableFuture.completedFuture(Collections.emptyList()));

            CompletableFuture<PolicyChangeResult> result = remediator.remediate_();
            PolicyChangeResult changeResult = result.get();

            assertEquals(RemediationResult.SUCCESS, changeResult.getResult());
            assertEquals("MS.TEAMS.1.9v1", changeResult.getPolicyId());
            assertEquals("All meeting policies secured - external chat disabled successfully", changeResult.getDesc());

            // Verify that changes were tracked for non-Tag policies only
            assertNotNull(changeResult.getChanges());
            assertEquals(2, changeResult.getChanges().size()); // Global and TestPolicy should be fixed (Tag policy filtered out)

            // Verify parameter change details
            for (ParameterChangeResult change : changeResult.getChanges()) {
                assertTrue(change.getParameter().contains("allowExternalNonTrustedMeetingChat"));
                assertEquals(true, change.getPrevValue()); // Original value was true
                assertEquals(false, change.getNewValue()); // Should be set to false
                assertEquals(ParameterChangeStatus.SUCCESS, change.getStatus());
            }

            // Verify PowerShell commands were executed for non-compliant, non-Tag policies
            verify(mockClient, times(2)).execute(commandCaptor.capture());

            // Verify the correct commands were executed
            List<CsTeamsCommand> capturedCommands = commandCaptor.getAllValues();
            assertEquals(2, capturedCommands.size());
            // Commands should be CsTeamsMeetingPolicy.SET commands
            capturedCommands.forEach(command -> 
                assertEquals("Set-CsTeamsMeetingPolicy", command.getName())
            );
        }

        @Test
        @DisplayName("Should detect all policies are compliant (requirement already met)")
        void testRemediateAllCompliant() throws Exception {
            // Create config with only compliant policies
            ObjectNode compliantConfig = objectMapper.createObjectNode();
            var compliantPoliciesArray = compliantConfig.putArray("meeting_policies");

            // Compliant policy - allowExternalNonTrustedMeetingChat = false
            compliantPoliciesArray.addObject()
                .put("Identity", "Global")
                .put("AllowExternalNonTrustedMeetingChat", false);

            // Another compliant policy
            compliantPoliciesArray.addObject()
                .put("Identity", "RestrictivePolicy")
                .put("AllowExternalNonTrustedMeetingChat", false);

            // Tag policy with compliant setting (should be filtered anyway)
            compliantPoliciesArray.addObject()
                .put("Identity", "Tag:CompliantPolicy")
                .put("AllowExternalNonTrustedMeetingChat", false);

            TeamsExternalMeetingChatRemediator compliantRemediator = 
                new TeamsExternalMeetingChatRemediator(mockClient, compliantConfig, mockRemediationConfig);

            CompletableFuture<PolicyChangeResult> result = compliantRemediator.remediate_();
            PolicyChangeResult changeResult = result.get();

            assertEquals(RemediationResult.REQUIREMENT_MET, changeResult.getResult());
            assertEquals("MS.TEAMS.1.9v1", changeResult.getPolicyId());

            // No PowerShell commands should be executed
            verify(mockClient, never()).execute(any(CsTeamsCommand.class));
        }

        @Test
        @DisplayName("Should handle null allowExternalNonTrustedMeetingChat values")
        void testRemediateNullValues() throws Exception {
            // Create config with null values (should be treated as non-compliant)
            ObjectNode nullConfig = objectMapper.createObjectNode();
            var nullPoliciesArray = nullConfig.putArray("meeting_policies");

            nullPoliciesArray.addObject()
                .put("Identity", "Global")
                .putNull("AllowExternalNonTrustedMeetingChat");

            TeamsExternalMeetingChatRemediator nullRemediator = 
                new TeamsExternalMeetingChatRemediator(mockClient, nullConfig, mockRemediationConfig);

            when(mockClient.execute(any()))
                .thenReturn(CompletableFuture.completedFuture(Collections.emptyList()));

            CompletableFuture<PolicyChangeResult> result = nullRemediator.remediate_();
            PolicyChangeResult changeResult = result.get();

            assertEquals(RemediationResult.SUCCESS, changeResult.getResult());
            assertEquals(1, changeResult.getChanges().size());

            ParameterChangeResult change = changeResult.getChanges().get(0);
            assertNull(change.getPrevValue()); // Was null
            assertEquals(false, change.getNewValue()); // Set to false

            verify(mockClient, times(1)).execute(any());
        }
    }

    @Nested
    @DisplayName("Configuration Error Handling Tests")
    class ConfigurationErrorTests {

        @Test
        @DisplayName("Should handle missing meeting policies configuration")
        void testRemediateNoPoliciesFound() throws Exception {
            // Create config with no meeting policies
            ObjectNode emptyConfig = objectMapper.createObjectNode();
            emptyConfig.putArray("meeting_policies");

            TeamsExternalMeetingChatRemediator emptyRemediator = 
                new TeamsExternalMeetingChatRemediator(mockClient, emptyConfig, mockRemediationConfig);

            CompletableFuture<PolicyChangeResult> result = emptyRemediator.remediate_();
            PolicyChangeResult changeResult = result.get();

            assertEquals(RemediationResult.FAILED, changeResult.getResult());
            assertEquals("MS.TEAMS.1.9v1", changeResult.getPolicyId());
            assertEquals("No policies found in configuration", changeResult.getDesc());

            verify(mockClient, never()).execute(any(CsTeamsCommand.class));
        }

        @Test
        @DisplayName("Should handle null configuration")
        void testRemediateNullConfig() throws Exception {
            TeamsExternalMeetingChatRemediator nullConfigRemediator = 
                new TeamsExternalMeetingChatRemediator(mockClient, null, mockRemediationConfig);

            CompletableFuture<PolicyChangeResult> result = nullConfigRemediator.remediate_();
            PolicyChangeResult changeResult = result.get();

            assertEquals(RemediationResult.FAILED, changeResult.getResult());
            assertEquals("No policies found in configuration", changeResult.getDesc());

            verify(mockClient, never()).execute(any());
        }
    }

    @Nested
    @DisplayName("PowerShell Execution Tests")
    class PowerShellExecutionTests {

        @Test
        @DisplayName("Should handle successful PowerShell execution")
        void testRemediateSuccess() throws Exception {
            // Mock successful PowerShell execution
            when(mockClient.execute(any()))
                .thenReturn(CompletableFuture.completedFuture(Collections.emptyList()));

            CompletableFuture<PolicyChangeResult> result = remediator.remediate_();
            PolicyChangeResult changeResult = result.get();

            assertEquals(RemediationResult.SUCCESS, changeResult.getResult());
            assertEquals("MS.TEAMS.1.9v1", changeResult.getPolicyId());
            assertEquals("All meeting policies secured - external chat disabled successfully", changeResult.getDesc());

            // Verify parameter change tracking
            assertEquals(2, changeResult.getChanges().size());
            changeResult.getChanges().forEach(change -> {
                assertEquals(ParameterChangeStatus.SUCCESS, change.getStatus());
                assertTrue(change.getParameter().contains("allowExternalNonTrustedMeetingChat"));
            });

            verify(mockClient, times(2)).execute(any());
        }

        @Test
        @DisplayName("Should handle partial PowerShell failures")
        void testRemediatePartialFailure() throws Exception {
            // Mock one success and one failure
            when(mockClient.execute(any()))
                .thenReturn(CompletableFuture.completedFuture(Collections.emptyList()))
                .thenReturn(CompletableFuture.failedFuture(new RuntimeException("PowerShell connection timeout")));

            CompletableFuture<PolicyChangeResult> result = remediator.remediate_();
            PolicyChangeResult changeResult = result.get();

            assertEquals(RemediationResult.PARTIAL_SUCCESS, changeResult.getResult());
            assertEquals("MS.TEAMS.1.9v1", changeResult.getPolicyId());
            assertTrue(changeResult.getDesc().contains("Secured 1 policies, failed to secure 1 policies"));

            // Verify that changes were tracked for both attempts
            assertNotNull(changeResult.getChanges());
            assertEquals(2, changeResult.getChanges().size());

            // One should be success, one should be failed
            long successCount = changeResult.getChanges().stream()
                .mapToLong(change -> change.getStatus() == ParameterChangeStatus.SUCCESS ? 1 : 0)
                .sum();
            long failedCount = changeResult.getChanges().stream()
                .mapToLong(change -> change.getStatus() == ParameterChangeStatus.FAILED ? 1 : 0)
                .sum();

            assertEquals(1, successCount);
            assertEquals(1, failedCount);

            verify(mockClient, times(2)).execute(any());
        }

        @Test
        @DisplayName("Should handle complete PowerShell failure")
        void testRemediateCompleteFailure() throws Exception {
            // Mock all PowerShell executions to fail
            when(mockClient.execute(any()))
                .thenReturn(CompletableFuture.failedFuture(new RuntimeException("PowerShell service unavailable")));

            CompletableFuture<PolicyChangeResult> result = remediator.remediate_();
            PolicyChangeResult changeResult = result.get();

            assertEquals(RemediationResult.FAILED, changeResult.getResult());
            assertEquals("MS.TEAMS.1.9v1", changeResult.getPolicyId());
            assertEquals("Failed to secure any meeting policies", changeResult.getDesc());

            // All changes should be marked as failed
            assertEquals(2, changeResult.getChanges().size());
            changeResult.getChanges().forEach(change -> 
                assertEquals(ParameterChangeStatus.FAILED, change.getStatus())
            );

            verify(mockClient, times(2)).execute(any());
        }
    }

    @Nested
    @DisplayName("Policy Filtering Tests")
    class PolicyFilteringTests {

        @Test
        @DisplayName("Should filter out Tag policies")
        void testFilterTagPolicies() throws Exception {
            // Config already has Tag:SomePolicy which should be filtered out
            when(mockClient.execute(any()))
                .thenReturn(CompletableFuture.completedFuture(Collections.emptyList()));

            CompletableFuture<PolicyChangeResult> result = remediator.remediate_();
            PolicyChangeResult changeResult = result.get();

            assertEquals(RemediationResult.SUCCESS, changeResult.getResult());

            // Verify only 2 policies were processed (Global and TestPolicy)
            // Tag:SomePolicy should be filtered out
            assertEquals(2, changeResult.getChanges().size());
            
            // Verify no Tag policies in the changes
            changeResult.getChanges().forEach(change -> 
                assertFalse(change.getParameter().contains("Tag:"))
            );

            verify(mockClient, times(2)).execute(any());
        }

        @Test
        @DisplayName("Should handle configuration with only Tag policies")
        void testOnlyTagPolicies() throws Exception {
            // Create config with only Tag policies
            ObjectNode tagOnlyConfig = objectMapper.createObjectNode();
            var tagPoliciesArray = tagOnlyConfig.putArray("meeting_policies");

            tagPoliciesArray.addObject()
                .put("Identity", "Tag:Policy1")
                .put("AllowExternalNonTrustedMeetingChat", true);

            tagPoliciesArray.addObject()
                .put("Identity", "Tag:Policy2")
                .put("AllowExternalNonTrustedMeetingChat", true);

            TeamsExternalMeetingChatRemediator tagOnlyRemediator = 
                new TeamsExternalMeetingChatRemediator(mockClient, tagOnlyConfig, mockRemediationConfig);

            CompletableFuture<PolicyChangeResult> result = tagOnlyRemediator.remediate_();
            PolicyChangeResult changeResult = result.get();

            assertEquals(RemediationResult.REQUIREMENT_MET, changeResult.getResult());

            // No commands should be executed since all policies are Tag policies
            verify(mockClient, never()).execute(any());
        }
    }

    @Nested
    @DisplayName("Rollback Functionality Tests")
    class RollbackTests {

        @Test
        @DisplayName("Should successfully rollback policy changes")
        void testRollbackSuccess() throws Exception {
            // Create a change result with parameter changes to rollback
            ParameterChangeResult change1 = new ParameterChangeResult()
                .parameter("allowExternalNonTrustedMeetingChat: Global")
                .prevValue(true) // Original value was true
                .newValue(false) // Was changed to false
                .status(ParameterChangeStatus.SUCCESS);

            ParameterChangeResult change2 = new ParameterChangeResult()
                .parameter("allowExternalNonTrustedMeetingChat: TestPolicy")
                .prevValue(true) // Original value was true
                .newValue(false) // Was changed to false
                .status(ParameterChangeStatus.SUCCESS);

            PolicyChangeResult originalResult = new PolicyChangeResult()
                .result(RemediationResult.SUCCESS)
                .policyId("MS.TEAMS.1.9v1")
                .changes(Arrays.asList(change1, change2));

            // Mock successful rollback execution
            when(mockClient.execute(any()))
                .thenReturn(CompletableFuture.completedFuture(Collections.emptyList()));

            CompletableFuture<PolicyChangeResult> rollbackResult = remediator.rollback(originalResult);
            PolicyChangeResult rollbackChangeResult = rollbackResult.get();

            assertEquals(RemediationResult.SUCCESS, rollbackChangeResult.getResult());
            assertEquals("MS.TEAMS.1.9v1", rollbackChangeResult.getPolicyId());
            assertEquals("All external meeting chat policies rolled back successfully", rollbackChangeResult.getDesc());

            // Verify rollback parameter changes
            assertEquals(2, rollbackChangeResult.getChanges().size());
            for (ParameterChangeResult change : rollbackChangeResult.getChanges()) {
                assertEquals(false, change.getPrevValue()); // Was false after remediation
                assertEquals(true, change.getNewValue()); // Rolled back to true
                assertEquals(ParameterChangeStatus.SUCCESS, change.getStatus());
            }

            // Verify rollback commands were executed
            verify(mockClient, times(2)).execute(any());
        }

        @Test
        @DisplayName("Should skip rollback for failed changes")
        void testRollbackWithFailedChange() throws Exception {
            // Create a change result with one successful and one failed change
            ParameterChangeResult successChange = new ParameterChangeResult()
                .parameter("allowExternalNonTrustedMeetingChat: Global")
                .prevValue(true)
                .newValue(false)
                .status(ParameterChangeStatus.SUCCESS);

            ParameterChangeResult failedChange = new ParameterChangeResult()
                .parameter("allowExternalNonTrustedMeetingChat: TestPolicy")
                .prevValue(true)
                .newValue(false)
                .status(ParameterChangeStatus.FAILED);

            PolicyChangeResult originalResult = new PolicyChangeResult()
                .result(RemediationResult.PARTIAL_SUCCESS)
                .policyId("MS.TEAMS.1.9v1")
                .changes(Arrays.asList(successChange, failedChange));

            // Mock successful rollback for the successful change
            when(mockClient.execute(any()))
                .thenReturn(CompletableFuture.completedFuture(Collections.emptyList()));

            CompletableFuture<PolicyChangeResult> rollbackResult = remediator.rollback(originalResult);
            PolicyChangeResult rollbackChangeResult = rollbackResult.get();

            assertEquals(RemediationResult.PARTIAL_SUCCESS, rollbackChangeResult.getResult());

            // Should have 2 rollback change results: 1 success, 1 skipped (failed)
            assertEquals(2, rollbackChangeResult.getChanges().size());

            long successRollbacks = rollbackChangeResult.getChanges().stream()
                .mapToLong(change -> change.getStatus() == ParameterChangeStatus.SUCCESS ? 1 : 0)
                .sum();
            long failedRollbacks = rollbackChangeResult.getChanges().stream()
                .mapToLong(change -> change.getStatus() == ParameterChangeStatus.FAILED ? 1 : 0)
                .sum();

            assertEquals(1, successRollbacks);
            assertEquals(1, failedRollbacks);

            // Only the successful change should be rolled back
            verify(mockClient, times(1)).execute(any());
        }

        @Test
        @DisplayName("Should handle rollback PowerShell failures")
        void testRollbackPowerShellFailure() throws Exception {
            ParameterChangeResult change = new ParameterChangeResult()
                .parameter("allowExternalNonTrustedMeetingChat: Global")
                .prevValue(true)
                .newValue(false)
                .status(ParameterChangeStatus.SUCCESS);

            PolicyChangeResult originalResult = new PolicyChangeResult()
                .result(RemediationResult.SUCCESS)
                .policyId("MS.TEAMS.1.9v1")
                .changes(List.of(change));

            // Mock rollback PowerShell failure
            when(mockClient.execute(any()))
                .thenReturn(CompletableFuture.failedFuture(new RuntimeException("Rollback failed")));

            CompletableFuture<PolicyChangeResult> rollbackResult = remediator.rollback(originalResult);
            PolicyChangeResult rollbackChangeResult = rollbackResult.get();

            assertEquals(RemediationResult.FAILED, rollbackChangeResult.getResult());
            assertEquals("MS.TEAMS.1.9v1", rollbackChangeResult.getPolicyId());

            // Rollback change should be marked as failed
            assertEquals(1, rollbackChangeResult.getChanges().size());
            assertEquals(ParameterChangeStatus.FAILED, rollbackChangeResult.getChanges().get(0).getStatus());

            verify(mockClient, times(1)).execute(any());
        }

        @Test
        @DisplayName("Should handle rollback exception during processing")
        void testRollbackException() throws Exception {
            // Create malformed change result to trigger exception
            ParameterChangeResult malformedChange = new ParameterChangeResult()
                .parameter("malformed_parameter") // Missing colon separator
                .prevValue(true)
                .newValue(false)
                .status(ParameterChangeStatus.SUCCESS);

            PolicyChangeResult originalResult = new PolicyChangeResult()
                .result(RemediationResult.SUCCESS)
                .policyId("MS.TEAMS.1.9v1")
                .changes(List.of(malformedChange));

            CompletableFuture<PolicyChangeResult> rollbackResult = remediator.rollback(originalResult);
            PolicyChangeResult rollbackChangeResult = rollbackResult.get();

            assertEquals(RemediationResult.FAILED, rollbackChangeResult.getResult());
            assertEquals("MS.TEAMS.1.9v1", rollbackChangeResult.getPolicyId());
            assertTrue(rollbackChangeResult.getDesc().contains("Index") || 
                      rollbackChangeResult.getDesc().contains("ArrayIndexOutOfBoundsException") ||
                      rollbackChangeResult.getDesc().contains("Exception"));

            // No PowerShell commands should be executed due to exception
            verify(mockClient, never()).execute(any());
        }
    }

    @Nested
    @DisplayName("Async CompletableFuture Pattern Tests")
    class AsyncPatternTests {

        @Test
        @DisplayName("Should handle async remediate() method returning JsonNode")
        void testAsyncRemediateJsonConversion() throws Exception {
            when(mockClient.execute(any()))
                .thenReturn(CompletableFuture.completedFuture(Collections.emptyList()));

            // Test that the async remediate() method completes successfully
            CompletableFuture<com.fasterxml.jackson.databind.JsonNode> jsonResult = remediator.remediate();
            
            // Verify the CompletableFuture completes without exception
            assertDoesNotThrow(() -> jsonResult.get());
            
            com.fasterxml.jackson.databind.JsonNode jsonNode = jsonResult.get();
            assertNotNull(jsonNode, "JsonNode should not be null");
            
            // Basic validation - ensure serialization worked
            assertTrue(jsonNode.isObject(), "JsonNode should be an object");
        }

        @Test
        @DisplayName("Should handle concurrent remediation requests")
        void testConcurrentRemediation() throws Exception {
            when(mockClient.execute(any()))
                .thenReturn(CompletableFuture.completedFuture(Collections.emptyList()));

            // Execute multiple concurrent remediations
            CompletableFuture<PolicyChangeResult> result1 = remediator.remediate_();
            CompletableFuture<PolicyChangeResult> result2 = remediator.remediate_();
            CompletableFuture<PolicyChangeResult> result3 = remediator.remediate_();

            // Wait for all to complete
            CompletableFuture.allOf(result1, result2, result3).get();

            PolicyChangeResult changeResult1 = result1.get();
            PolicyChangeResult changeResult2 = result2.get();
            PolicyChangeResult changeResult3 = result3.get();

            // All should succeed
            assertEquals(RemediationResult.SUCCESS, changeResult1.getResult());
            assertEquals(RemediationResult.SUCCESS, changeResult2.getResult());
            assertEquals(RemediationResult.SUCCESS, changeResult3.getResult());

            // Verify PowerShell was called for each remediation
            verify(mockClient, times(6)).execute(any()); // 3 remediations * 2 policies each
        }
    }

    @Nested
    @DisplayName("Property Value Validation Tests")
    class PropertyValueValidationTests {

        @Test
        @DisplayName("Should correctly set allowExternalNonTrustedMeetingChat property")
        void testAllowExternalNonTrustedMeetingChatProperty() throws Exception {
            when(mockClient.execute(any()))
                .thenReturn(CompletableFuture.completedFuture(Collections.emptyList()));

            CompletableFuture<PolicyChangeResult> result = remediator.remediate_();
            PolicyChangeResult changeResult = result.get();

            // Verify the correct property values are being set
            for (ParameterChangeResult change : changeResult.getChanges()) {
                assertEquals(false, change.getNewValue()); // Should be set to false
                assertEquals(true, change.getPrevValue()); // Original was true
                assertTrue(change.getParameter().contains("allowExternalNonTrustedMeetingChat"));
                assertTrue(change.getParameter().contains("Global") || 
                          change.getParameter().contains("TestPolicy"));
            }
        }

        @Test
        @DisplayName("Should track parameter changes with timestamps")
        void testParameterChangeTimestamps() throws Exception {
            when(mockClient.execute(any()))
                .thenReturn(CompletableFuture.completedFuture(Collections.emptyList()));

            CompletableFuture<PolicyChangeResult> result = remediator.remediate_();
            PolicyChangeResult changeResult = result.get();

            // Verify all parameter changes have timestamps
            for (ParameterChangeResult change : changeResult.getChanges()) {
                assertNotNull(change.getTimeStamp());
                // Timestamp should be recent (within last minute)
                long timeDiff = java.time.Instant.now().toEpochMilli() - change.getTimeStamp().toEpochMilli();
                assertTrue(timeDiff < 60000, "Timestamp should be within last minute");
            }
        }
    }
}