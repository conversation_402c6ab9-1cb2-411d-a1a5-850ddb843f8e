package io.syrix.products.microsoft.teams.remediation;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.products.microsoft.base.IPolicyRemediator;
import io.syrix.products.microsoft.base.IPolicyRemediatorRollback;
import io.syrix.products.microsoft.base.ParameterChangeResult;
import io.syrix.products.microsoft.base.ParameterChangeStatus;
import io.syrix.products.microsoft.base.PolicyChangeResult;
import io.syrix.products.microsoft.base.PolicyRemediator;
import io.syrix.products.microsoft.base.RemediationResult;
import io.syrix.datamodel.task.remediation.teams.TeamsRemediationConfig;
import io.syrix.products.microsoft.teams.model.TeamsTenantFederationConfiguration;
import io.syrix.protocols.client.PowerShellTeamsClient;
import io.syrix.protocols.client.teams.powershell.command.CsTeamsCommand;
import io.syrix.protocols.client.teams.powershell.command.types.TenantFederationConfiguration;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;

@PolicyRemediator("MS.TEAMS.3.1v1")
public class TeamsAllowPublicUsersRemediator extends TeamsRemediatorBase implements IPolicyRemediatorRollback {

	public TeamsAllowPublicUsersRemediator(PowerShellTeamsClient client, ObjectNode configNode, TeamsRemediationConfig remediationConfig) {
		super(client, configNode, remediationConfig);
	}

	public TeamsAllowPublicUsersRemediator(PowerShellTeamsClient client) {
		super(client, null, null);
	}

	@Override
	public CompletableFuture<JsonNode> remediate() {
		return remediate_().thenApply(jsonMapper::valueToTree);
	}

	@Override
	public CompletableFuture<PolicyChangeResult> remediate_() {
		List<TeamsTenantFederationConfiguration> configs = getFederationConfigurations();

		return executeRemediationWorkflow(
				configs,
				configuration -> configuration.allowPublicUsers,
				configuration -> {
					TenantFederationConfiguration prevConfig = new TenantFederationConfiguration();
					prevConfig.identity = configuration.identity;
					prevConfig.allowPublicUsers = configuration.allowPublicUsers;

					TenantFederationConfiguration newConfig = new TenantFederationConfiguration();
					newConfig.identity = configuration.identity;
					newConfig.allowPublicUsers = false;

					return fixConfig(prevConfig, newConfig);
				},
				"All federation configurations fixed successfully",
				"Failed to fix any federation configurations",
				"Fixed %d configurations, failed to fix %d configurations"
		);
	}

	private CompletableFuture<PolicyChangeResult> fixConfig(TenantFederationConfiguration prevConfig, TenantFederationConfiguration newConfig) {
		ParameterChangeResult paramChange = new ParameterChangeResult()
				.timeStamp(Instant.now())
				.parameter("allowPublicUsers: " + prevConfig.identity)
				.prevValue(prevConfig.allowPublicUsers)
				.newValue(newConfig.allowPublicUsers);

		return client.execute(CsTeamsCommand.CsTenantFederationConfiguration.SET(newConfig))
				.thenApply(config -> {
					paramChange.status(ParameterChangeStatus.SUCCESS);
					return IPolicyRemediator.success_(getPolicyId(), "Federation Configuration: " + newConfig.identity + " fixed", List.of(paramChange));
				})
				.exceptionally(ex -> {
					logger.warn("Failed to update Federation Configuration: {} , errMsg: {}", newConfig.identity, ex.getMessage());
					paramChange.status(ParameterChangeStatus.FAILED);
					return IPolicyRemediator.failed_(getPolicyId(), "Failed to update Federation Configuration: " + ex.getMessage(), List.of(paramChange));
				});
	}


	@Override
	public CompletableFuture<PolicyChangeResult> rollback(PolicyChangeResult fixResult) {
		try {
			List<ParameterChangeResult> changes = fixResult.getChanges();
			List<CompletableFuture<PolicyChangeResult>> results = new ArrayList<>();

			for (ParameterChangeResult change : changes) {
				String identity = change.getParameter().split(": ")[1];
				boolean newValue = Boolean.parseBoolean(change.getPrevValue().toString());
				boolean curValue = Boolean.parseBoolean(change.getNewValue().toString());

				TenantFederationConfiguration prevConfig = new TenantFederationConfiguration();
				prevConfig.identity = identity;
				prevConfig.allowPublicUsers = curValue;

				TenantFederationConfiguration newConfig = new TenantFederationConfiguration();
				newConfig.identity = identity;
				newConfig.allowPublicUsers = newValue;

				if (change.getStatus() == ParameterChangeStatus.SUCCESS) {
					results.add(fixConfig(prevConfig, newConfig));
				} else {
					ParameterChangeResult paramChange = new ParameterChangeResult()
							.timeStamp(Instant.now())
							.parameter("allowPublicUsers: " + prevConfig.identity)
							.prevValue(prevConfig.allowPublicUsers)
							.newValue(newConfig.allowPublicUsers)
							.status(ParameterChangeStatus.FAILED);

					logger.error("Rollback the policy: {} identity: {} skipped", getPolicyId(), identity);
					results.add(CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(),
							"Rollback the policy " + getPolicyId() + " identity: " + identity + " skipped", List.of(paramChange))));
				}
			}

			return combineResults(results,
					"All public user policies rolled back successfully",
					"Failed to rollback any public user policies",
					"Rolled back %d policies, failed to rollback %d policies");
		} catch (Exception ex) {
			logger.error("Rollback the policy {} failed", getPolicyId(), ex);
			return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), ex.getMessage()));
		}
	}
}
