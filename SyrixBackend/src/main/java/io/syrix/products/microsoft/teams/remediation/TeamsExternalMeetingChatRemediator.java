package io.syrix.products.microsoft.teams.remediation;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.products.microsoft.base.IPolicyRemediator;
import io.syrix.products.microsoft.base.IPolicyRemediatorRollback;
import io.syrix.products.microsoft.base.ParameterChangeResult;
import io.syrix.products.microsoft.base.ParameterChangeStatus;
import io.syrix.products.microsoft.base.PolicyChangeResult;
import io.syrix.products.microsoft.base.PolicyRemediator;
import io.syrix.products.microsoft.base.RemediationResult;
import io.syrix.datamodel.task.remediation.teams.TeamsRemediationConfig;
import io.syrix.products.microsoft.teams.model.TeamsMeetingPolicy;
import io.syrix.protocols.client.PowerShellTeamsClient;
import io.syrix.protocols.client.teams.powershell.command.CsTeamsCommand;
import io.syrix.protocols.client.teams.powershell.command.types.MeetingPolicy;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;

@PolicyRemediator("MS.TEAMS.1.9v1")
public class TeamsExternalMeetingChatRemediator extends TeamsRemediatorBase implements IPolicyRemediatorRollback {

	public TeamsExternalMeetingChatRemediator(PowerShellTeamsClient client, ObjectNode configNode, TeamsRemediationConfig remediationConfig) {
		super(client, configNode, remediationConfig);
	}

	public TeamsExternalMeetingChatRemediator(PowerShellTeamsClient client) {
		super(client, null, null);
	}

	@Override
	public CompletableFuture<JsonNode> remediate() {
		return remediate_().thenApply(jsonMapper::valueToTree);
	}

	@Override
	public CompletableFuture<PolicyChangeResult> remediate_() {
		List<TeamsMeetingPolicy> policies = getTeamsMeetingPolicies();

		return executeRemediationWorkflow(
				policies,
				policy -> !Boolean.FALSE.equals(policy.allowExternalNonTrustedMeetingChat) && !policy.identity.startsWith("Tag:"),
				this::fixPolicy,
				"All meeting policies secured - external chat disabled successfully",
				"Failed to secure any meeting policies",
				"Secured %d policies, failed to secure %d policies"
		);
	}

	private CompletableFuture<PolicyChangeResult> fixPolicy(TeamsMeetingPolicy policy) {
		MeetingPolicy meetingPolicy = new MeetingPolicy();
		meetingPolicy.identity = policy.identity;
		meetingPolicy.allowExternalNonTrustedMeetingChat = false;

		ParameterChangeResult chatEnabledParam = new ParameterChangeResult()
				.timeStamp(Instant.now())
				.parameter("allowExternalNonTrustedMeetingChat: " + policy.identity)
				.prevValue(policy.allowExternalNonTrustedMeetingChat)
				.newValue(meetingPolicy.allowExternalNonTrustedMeetingChat);

		return client.execute(CsTeamsCommand.CsTeamsMeetingPolicy.SET(meetingPolicy))
				.thenApply(meetingPolicies -> {
					chatEnabledParam.status(ParameterChangeStatus.SUCCESS);
					return IPolicyRemediator.success_(getPolicyId(), "External meeting chat disabled for policy: " + policy.identity, List.of(chatEnabledParam));
				})
				.exceptionally(ex -> {
					logger.warn("Failed to update Meeting Policy: {} , errMsg: {}", policy.identity, ex.getMessage());
					chatEnabledParam.status(ParameterChangeStatus.FAILED);
					return IPolicyRemediator.failed_(getPolicyId(), "Failed to update Meeting Policy: " + ex.getMessage(), List.of(chatEnabledParam));
				});
	}


	@Override
	public CompletableFuture<PolicyChangeResult> rollback(PolicyChangeResult fixResult) {
		try {
			List<ParameterChangeResult> changes = fixResult.getChanges();
			List<CompletableFuture<PolicyChangeResult>> results = new ArrayList<>();

			for (ParameterChangeResult change : changes) {
				String identity = change.getParameter().split(": ")[1];
				Boolean allowExternalNonTrustedMeetingChat = (Boolean) change.getPrevValue();
				Boolean prevAllowExternalNonTrustedMeetingChat = (Boolean) change.getNewValue();

				MeetingPolicy meetingPolicy = new MeetingPolicy();
				meetingPolicy.identity = identity;
				meetingPolicy.allowExternalNonTrustedMeetingChat = allowExternalNonTrustedMeetingChat;

				ParameterChangeResult chatEnabledParam = new ParameterChangeResult()
						.timeStamp(Instant.now())
						.parameter("allowExternalNonTrustedMeetingChat: " + identity)
						.prevValue(prevAllowExternalNonTrustedMeetingChat)
						.newValue(allowExternalNonTrustedMeetingChat);

				if (change.getStatus() != ParameterChangeStatus.SUCCESS) {
					logger.error("Rollback the policy: {} identity: {} skipped", getPolicyId(), identity);
					chatEnabledParam.status(ParameterChangeStatus.FAILED);
					results.add(CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(),
							"Rollback the policy " + getPolicyId() + " identity: " + identity + " skipped", List.of(chatEnabledParam))));
					continue;
				}

				CompletableFuture<PolicyChangeResult> result = client.execute(CsTeamsCommand.CsTeamsMeetingPolicy.SET(meetingPolicy))
						.thenApply(jsonNode -> {
							chatEnabledParam.status(ParameterChangeStatus.SUCCESS);
							return IPolicyRemediator.success_(getPolicyId(), "Successfully rolled back external meeting chat policy", List.of(chatEnabledParam));
						})
						.exceptionally(ex -> {
							chatEnabledParam.status(ParameterChangeStatus.FAILED);
							logger.error("Exception during external meeting chat policy rollback", ex);
							return IPolicyRemediator.failed_(getPolicyId(), ex.getMessage(), List.of(chatEnabledParam));
						});

				results.add(result);
			}

			return combineResults(results, "All external meeting chat policies rolled back successfully",
					"Failed to rollback any external meeting chat policies",
					"Rolled back %d policies, failed to rollback %d policies");
		} catch (Exception ex) {
			logger.error("Rollback the policy {} failed", getPolicyId(), ex);
			return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), ex.getMessage()));
		}
	}
}