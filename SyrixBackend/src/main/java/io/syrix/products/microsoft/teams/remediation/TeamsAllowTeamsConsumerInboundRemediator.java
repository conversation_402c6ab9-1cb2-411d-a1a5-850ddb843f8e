package io.syrix.products.microsoft.teams.remediation;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.products.microsoft.base.IPolicyRemediator;
import io.syrix.products.microsoft.base.IPolicyRemediatorRollback;
import io.syrix.products.microsoft.base.ParameterChangeResult;
import io.syrix.products.microsoft.base.ParameterChangeStatus;
import io.syrix.products.microsoft.base.PolicyChangeResult;
import io.syrix.products.microsoft.base.PolicyRemediator;
import io.syrix.products.microsoft.base.RemediationResult;
import io.syrix.datamodel.task.remediation.teams.TeamsRemediationConfig;
import io.syrix.products.microsoft.teams.model.TeamsTenantFederationConfiguration;
import io.syrix.protocols.client.PowerShellTeamsClient;
import io.syrix.protocols.client.teams.powershell.command.CsTeamsCommand;
import io.syrix.protocols.client.teams.powershell.command.types.TenantFederationConfiguration;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;

@PolicyRemediator("MS.TEAMS.2.2v1")
public class TeamsAllowTeamsConsumerInboundRemediator extends TeamsRemediatorBase implements IPolicyRemediatorRollback {

	private final ObjectMapper objectMapper = new ObjectMapper();

	public TeamsAllowTeamsConsumerInboundRemediator(PowerShellTeamsClient client, ObjectNode configNode, TeamsRemediationConfig remediationConfig) {
		super(client, configNode, remediationConfig);
	}

	public TeamsAllowTeamsConsumerInboundRemediator(PowerShellTeamsClient client) {
		super(client, null, null);
	}

	@Override
	public CompletableFuture<JsonNode> remediate() {
		return remediate_().thenApply(jsonMapper::valueToTree);
	}

	@Override
	public CompletableFuture<PolicyChangeResult> remediate_() {
		List<TeamsTenantFederationConfiguration> federationConfigurations = getFederationConfigurations();

		return executeRemediationWorkflow(
				federationConfigurations,
				config -> config.allowTeamsConsumer || config.allowTeamsConsumerInbound,
				this::fixConfigWrapper,
				"All federation configurations secured - Teams consumer access disabled successfully",
				"Failed to secure any federation configurations",
				"Secured %d configurations, failed to secure %d configurations"
		);
	}

	private CompletableFuture<PolicyChangeResult> fixConfig_(TenantFederationConfiguration curConfig, TenantFederationConfiguration newConfig) {
		try {
			FederationConfig prevConfig = new FederationConfig(curConfig.allowTeamsConsumer, curConfig.allowTeamsConsumerInbound);
			FederationConfig newFederationConfig = new FederationConfig(newConfig.allowTeamsConsumer, newConfig.allowTeamsConsumerInbound);

			ParameterChangeResult param = new ParameterChangeResult()
					.timeStamp(Instant.now())
					.parameter("federationConfig: " + curConfig.identity)
					.prevValue(objectMapper.writeValueAsString(prevConfig))
					.newValue(objectMapper.writeValueAsString(newFederationConfig));

			return client.execute(CsTeamsCommand.CsTenantFederationConfiguration.SET(newConfig))
					.thenApply(config -> {
						param.status(ParameterChangeStatus.SUCCESS);
						return IPolicyRemediator.success_(getPolicyId(), "Federation Configuration fixed: " + newConfig.identity, List.of(param));
					})
					.exceptionally(ex -> {
						logger.warn("Failed to update Federation Configuration: {} , errMsg: {}", newConfig.identity, ex.getMessage());
						param.status(ParameterChangeStatus.FAILED);
						return IPolicyRemediator.failed_(getPolicyId(), "Failed to update Federation Configuration: " + ex.getMessage(), List.of(param));
					});
		} catch (Exception ex) {
			logger.error("Failed to serialize configuration", ex);
			return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), "Failed to serialize configuration: " + ex.getMessage()));
		}
	}

	/**
	 * Wrapper method to handle the complex logic needed for federation configurations
	 */
	private CompletableFuture<PolicyChangeResult> fixConfigWrapper(TeamsTenantFederationConfiguration curTenantConfig) {
		TenantFederationConfiguration curConfig = new TenantFederationConfiguration();
		curConfig.identity = curTenantConfig.identity;
		curConfig.allowTeamsConsumer = curTenantConfig.allowTeamsConsumer;
		curConfig.allowTeamsConsumerInbound = curTenantConfig.allowTeamsConsumerInbound;

		TenantFederationConfiguration newConfig = new TenantFederationConfiguration();
		newConfig.identity = curTenantConfig.identity;
		newConfig.allowTeamsConsumer = false;
		newConfig.allowTeamsConsumerInbound = false;

		return fixConfig_(curConfig, newConfig);
	}

	@Override
	public CompletableFuture<PolicyChangeResult> rollback(PolicyChangeResult fixResult) {
		try {
			List<ParameterChangeResult> changes = fixResult.getChanges();
			if (changes == null || changes.isEmpty()) {
				return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), "No changes found in fix result"));
			}

			List<CompletableFuture<PolicyChangeResult>> results = new ArrayList<>();

			for (ParameterChangeResult change : changes) {
				String[] parts = change.getParameter().split(": ");
				String identity = parts[1];

				TenantFederationConfiguration sourceConfig = new TenantFederationConfiguration();
				TenantFederationConfiguration remediationConfig = new TenantFederationConfiguration();
				sourceConfig.identity = identity;
				remediationConfig.identity = identity;

				FederationConfig prevConfig = objectMapper.readValue(change.getPrevValue().toString(), FederationConfig.class);
				FederationConfig newConfig = objectMapper.readValue(change.getNewValue().toString(), FederationConfig.class);

				sourceConfig.allowTeamsConsumer = prevConfig.allowTeamsConsumer;
				sourceConfig.allowTeamsConsumerInbound = prevConfig.allowTeamsConsumerInbound;
				remediationConfig.allowTeamsConsumer = newConfig.allowTeamsConsumer;
				remediationConfig.allowTeamsConsumerInbound = newConfig.allowTeamsConsumerInbound;

				if (change.getStatus() != ParameterChangeStatus.SUCCESS) {
					ParameterChangeResult param = new ParameterChangeResult()
							.timeStamp(Instant.now())
							.parameter("federationConfig: " + identity)
							.prevValue(change.getNewValue().toString())
							.newValue(change.getPrevValue().toString())
							.status(ParameterChangeStatus.FAILED);

					results.add(CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(),
							"Rollback the policy " + getPolicyId() + " identity: " + identity + " skipped", List.of(param))));
					continue;
				}

				results.add(fixConfig_(remediationConfig, sourceConfig));
			}

			return combineResults(results,
					"All Teams consumer inbound policies rolled back successfully",
					"Failed to rollback any Teams consumer inbound policies",
					"Rolled back %d policies, failed to rollback %d policies");
		} catch (Exception ex) {
			logger.error("Rollback the policy {} failed", getPolicyId(), ex);
			return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), ex.getMessage()));
		}
	}

	private static class FederationConfig {
		public boolean allowTeamsConsumer;
		public boolean allowTeamsConsumerInbound;

		public FederationConfig(boolean allowTeamsConsumer, boolean allowTeamsConsumerInbound) {
			this.allowTeamsConsumer = allowTeamsConsumer;
			this.allowTeamsConsumerInbound = allowTeamsConsumerInbound;
		}

		@SuppressWarnings("unused") // necessary for jackson
		public FederationConfig() {
		}
	}

}
