package io.syrix.products.microsoft.sharepoint.remediation;

import com.fasterxml.jackson.databind.JsonNode;
import io.syrix.products.microsoft.base.IPolicyRemediator;
import io.syrix.products.microsoft.base.IPolicyRemediatorRollback;
import io.syrix.products.microsoft.base.ParameterChangeResult;
import io.syrix.products.microsoft.base.PolicyChangeResult;
import io.syrix.products.microsoft.base.PolicyRemediator;
import io.syrix.products.microsoft.sharepoint.SharepointConstants;
import io.syrix.products.microsoft.sharepoint.model.SharePointTenantProperties;
import io.syrix.datamodel.task.remediation.sharepoint.SharepointRemediationConfig;
import io.syrix.protocols.client.PowerShellSharepointClient;
import io.syrix.protocols.client.sharepoint.powershell.cscom.command.SPShellCommand;
import io.syrix.protocols.client.sharepoint.powershell.cscom.command.mstypes.GeneralResult;
import io.syrix.protocols.utils.Retry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.CompletableFuture;

/*
    This policy implements MS.SHAREPOINT.5.1v1 - Ensure Office 365 SharePoint infected files are disallowed for download.
    Based on CIS MS365 benchmark 7.3.1: Ensure Office 365 SharePoint infected files are disallowed for download (Automated)
    
    The policy ensures that SharePoint files detected as infected by Defender for Office 365 
    are blocked from download. This prevents inadvertent sharing of malicious files.
    
    PowerShell commands:
    Get-SPOTenant | Select-Object DisallowInfectedFileDownload
    Set-SPOTenant –DisallowInfectedFileDownload $true
    
    Connection: Connect-SPOService -Url https://tenant-admin.sharepoint.com
 */
@PolicyRemediator("MS.SHAREPOINT.5.1v1")
public class SharePointMalwareProtectionRemediator extends SPRemediatorBase implements IPolicyRemediatorRollback {
    private static final Logger log = LoggerFactory.getLogger(SharePointMalwareProtectionRemediator.class);

    // constructor for Rollback interface
    public SharePointMalwareProtectionRemediator(PowerShellSharepointClient client, SharePointTenantProperties tenant) {
        super(client, tenant, null);
    }

    private CompletableFuture<PolicyChangeResult> runCommand(boolean enableMalwareProtection, Boolean prevValue) {
        try {
            SPShellCommand<GeneralResult> command = SPShellCommand.PnPTenant.SET(
                tenant.objectIdentity, 
                SharepointConstants.DISALLOW_INFECTED_FILE_DOWNLOAD_PROPERTY, 
                enableMalwareProtection, 
                prevValue
            );
            
            return Retry.executeWithRetry(() -> client.execute_(command), MAX_RETRY)
                    .thenApply(this::checkResult);
        } catch (Exception ex) {
            log.error("Run command for policy {} failed", getPolicyId(), ex);
            return CompletableFuture.failedFuture(ex);
        }
    }

    @Override
    public CompletableFuture<PolicyChangeResult> remediate_() {
        // Validate tenant properties are available
        if (tenant == null) {
            return CompletableFuture.completedFuture(
                IPolicyRemediator.failed_(getPolicyId(), "SharePoint tenant properties are not available")
            );
        }

        Boolean currentValue = tenant.disallowInfectedFileDownload;
        
        // If already enabled, policy is compliant
        if (Boolean.TRUE.equals(currentValue)) {
            return CompletableFuture.completedFuture(
                IPolicyRemediator.success_(getPolicyId(), SharepointConstants.MALWARE_PROTECTION_SUCCESS)
            );
        }

        return runCommand(true, currentValue)
                .exceptionally(ex -> {
                    log.error("Remediate policy {} finished with exception", getPolicyId(), ex);
                    return IPolicyRemediator.failed_(getPolicyId(), "Failed to enable malware protection: " + ex.getMessage());
                });
    }

    @Override
    public CompletableFuture<JsonNode> remediate() {
        return remediate_().thenApply(res -> jsonMapper.valueToTree(res));
    }

    @Override
    public CompletableFuture<PolicyChangeResult> rollback(PolicyChangeResult fixResult) {
        try {
            // Validate rollback parameters
            if (fixResult == null) {
                log.error("Fix result is null for policy {} rollback", getPolicyId());
                return CompletableFuture.completedFuture(
                    IPolicyRemediator.failed_(getPolicyId(), "No fix result provided for rollback")
                );
            }
            
            if (fixResult.getChanges() == null || fixResult.getChanges().isEmpty()) {
                log.warn("No changes found in fix result for policy {}", getPolicyId());
                return CompletableFuture.completedFuture(
                    IPolicyRemediator.failed_(getPolicyId(), "No changes to rollback")
                );
            }
            
            // Validate tenant is still available
            if (tenant == null) {
                log.error("SharePoint tenant properties not available for policy {} rollback", getPolicyId());
                return CompletableFuture.completedFuture(
                    IPolicyRemediator.failed_(getPolicyId(), "SharePoint tenant properties not available for rollback")
                );
            }

            ParameterChangeResult changeResult = fixResult.getChanges().getFirst();
            Boolean previousValue = Boolean.valueOf(changeResult.getPrevValue().toString());
            Boolean newValue = Boolean.valueOf(changeResult.getNewValue().toString());

            return runCommand(previousValue != null ? previousValue : false, newValue)
                    .exceptionally(ex -> {
                        log.error("Rollback policy {} finished with exception", getPolicyId(), ex);
                        return IPolicyRemediator.failed_(getPolicyId(), "Rollback failed: " + ex.getMessage());
                    });
        } catch (Exception ex) {
            log.error("Rollback policy {} failed", getPolicyId(), ex);
            return CompletableFuture.completedFuture(
                IPolicyRemediator.failed_(getPolicyId(), "Rollback failed: " + ex.getMessage())
            );
        }
    }
}