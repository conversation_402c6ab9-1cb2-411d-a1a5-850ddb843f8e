package io.syrix.products.microsoft.teams.remediation;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.products.microsoft.base.IPolicyRemediator;
import io.syrix.products.microsoft.base.IPolicyRemediatorRollback;
import io.syrix.products.microsoft.base.ParameterChangeResult;
import io.syrix.products.microsoft.base.ParameterChangeStatus;
import io.syrix.products.microsoft.base.PolicyChangeResult;
import io.syrix.products.microsoft.base.PolicyRemediator;
import io.syrix.products.microsoft.base.RemediationResult;
import io.syrix.products.microsoft.teams.TeamsConstants;
import io.syrix.datamodel.task.remediation.teams.TeamsRemediationConfig;
import io.syrix.products.microsoft.teams.model.TeamsTenantFederationConfiguration;
import io.syrix.protocols.client.PowerShellTeamsClient;
import io.syrix.protocols.client.teams.powershell.command.CsTeamsCommand;
import io.syrix.protocols.client.teams.powershell.command.types.TenantFederationConfiguration;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;

@PolicyRemediator("MS.TEAMS.2.3v1")
public class TeamsAllowTeamsConsumerRemediator extends TeamsRemediatorBase implements IPolicyRemediatorRollback {

	public TeamsAllowTeamsConsumerRemediator(PowerShellTeamsClient client, ObjectNode configNode, TeamsRemediationConfig remediationConfig) {
		super(client, configNode, remediationConfig);
	}

	public TeamsAllowTeamsConsumerRemediator(PowerShellTeamsClient client) {
		super(client, null, null);
	}

	@Override
	public CompletableFuture<JsonNode> remediate() {
		return remediate_().thenApply(jsonMapper::valueToTree);
	}

	@Override
	public CompletableFuture<PolicyChangeResult> remediate_() {
		List<TeamsTenantFederationConfiguration> federationConfigurations = getFederationConfigurations();

		return executeRemediationWorkflow(
				federationConfigurations,
				configuration -> configuration.allowTeamsConsumer,
				this::fixConfig_,
				TeamsConstants.TEAMS_CONSUMER_SUCCESS_MESSAGE,
				"Failed to fix any federation configurations",
				"Fixed %d configurations, failed to fix %d configurations"
		);
	}

	private CompletableFuture<PolicyChangeResult> fixConfig_(TeamsTenantFederationConfiguration fedConfig) {
		TenantFederationConfiguration configuration = new TenantFederationConfiguration();
		configuration.identity = fedConfig.identity;
		configuration.allowTeamsConsumer = false;

		ParameterChangeResult consumerParam = new ParameterChangeResult()
				.timeStamp(Instant.now())
				.parameter("allowTeamsConsumer: " + fedConfig.identity)
				.prevValue(fedConfig.allowTeamsConsumer)
				.newValue(configuration.allowTeamsConsumer);

		return client.execute(CsTeamsCommand.CsTenantFederationConfiguration.SET(configuration))
				.thenApply(config -> {
					consumerParam.status(ParameterChangeStatus.SUCCESS);
					return IPolicyRemediator.success_(getPolicyId(), "Federation Configuration fixed: " + configuration.identity, List.of(consumerParam));
				})
				.exceptionally(ex -> {
					logger.warn("Failed to update Federation Configuration: {} , errMsg: {}", configuration.identity, ex.getMessage());
					consumerParam.status(ParameterChangeStatus.FAILED);
					return IPolicyRemediator.failed_(getPolicyId(), "Failed to update Federation Configuration: " + ex.getMessage(), List.of(consumerParam));
				});
	}


	@Override
	public CompletableFuture<PolicyChangeResult> rollback(PolicyChangeResult fixResult) {
		try {
			List<ParameterChangeResult> changes = fixResult.getChanges();
			List<CompletableFuture<PolicyChangeResult>> results = new ArrayList<>();

			for (ParameterChangeResult change : changes) {
				boolean allowTeamsConsumer = Boolean.parseBoolean(change.getPrevValue().toString());
				boolean prevAllowTeamsConsumer = Boolean.parseBoolean(change.getNewValue().toString());

				TenantFederationConfiguration configuration = new TenantFederationConfiguration();
				configuration.identity = change.getParameter().split(": ")[1];
				configuration.allowTeamsConsumer = allowTeamsConsumer;

				ParameterChangeResult consumerParam = new ParameterChangeResult()
						.timeStamp(Instant.now())
						.parameter("allowTeamsConsumer: " + configuration.identity)
						.prevValue(prevAllowTeamsConsumer)
						.newValue(allowTeamsConsumer);

				if (change.getStatus() != ParameterChangeStatus.SUCCESS) {
					logger.error("Rollback the policy: {} identity: {} skipped", getPolicyId(), configuration.identity);
					consumerParam.status(ParameterChangeStatus.FAILED);
					results.add(CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(),
							"Rollback the policy " + getPolicyId() + " identity: " + configuration.identity + " skipped", List.of(consumerParam))));
					continue;
				}

				CompletableFuture<PolicyChangeResult> result = client.execute(CsTeamsCommand.CsTenantFederationConfiguration.SET(configuration))
						.thenApply(jsonNode -> {
							consumerParam.status(ParameterChangeStatus.SUCCESS);
							return IPolicyRemediator.success_(getPolicyId(), "Successfully rolled back teams consumer policy", List.of(consumerParam));
						})
						.exceptionally(ex -> {
							consumerParam.status(ParameterChangeStatus.FAILED);
							logger.error("Exception during teams consumer policy rollback", ex);
							return IPolicyRemediator.failed_(getPolicyId(), ex.getMessage(), List.of(consumerParam));
						});

				results.add(result);
			}

			return combineResults(results,
					"All teams consumer policies rolled back successfully",
					"Failed to rollback any teams consumer policies",
					"Rolled back %d policies, failed to rollback %d policies");
		} catch (Exception ex) {
			logger.error("Rollback the policy {} failed", getPolicyId(), ex);
			return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), ex.getMessage()));
		}
	}
}
