package io.syrix.products.microsoft.sharepoint;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import io.syrix.common.exceptions.SyrixRuntimeException;
import io.syrix.datamodel.report.ConfigurationServiceType;
import io.syrix.products.microsoft.sharepoint.model.SharePointTenantProperties;
import io.syrix.products.microsoft.sharepoint.model.SharePointSite;
import io.syrix.protocols.client.sharepoint.powershell.cscom.command.mstypes.SiteProperties;

import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.common.exceptions.ConfigurationExportException;
import io.syrix.common.metrics.MetricsCollector;
import io.syrix.common.model.ConfigurationResult;
import io.syrix.protocols.client.PowerShellSharepointClient;
import io.syrix.protocols.client.graph.response.Site;
import io.syrix.protocols.client.sharepoint.powershell.cscom.command.SPShellCommand;
import io.syrix.protocols.client.sharepoint.powershell.cscom.command.mstypes.AllowToBeDeleted;
import io.syrix.protocols.client.sharepoint.powershell.cscom.command.mstypes.TenantProperties;
import io.syrix.protocols.model.GraphRequest;
import io.syrix.products.microsoft.base.BaseConfigurationService;
import io.syrix.protocols.utils.Retry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Duration;
import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

import static io.syrix.products.microsoft.sharepoint.SharepointConstants.CONFIG_KEY_SPO_TENANT;

/**
 * Service for managing and exporting SharePoint Online and OneDrive for Business configurations.
 * Provides functionality for retrieving SharePoint and OneDrive settings, policies, and sharing configurations.
 * <p>
 * Artur - The returned answer is not one-to-one. For now, I have chosen the closest analogous one. In the future, combining the results of different commands may be necessary.
 */
public class SharePointConfigurationService extends BaseConfigurationService {
    private static final Logger logger = LoggerFactory.getLogger(SharePointConfigurationService.class);
    private static final int MAX_RETRY = 3;
    private static final Duration DEFAULT_TIMEOUT = Duration.ofMinutes(5);
    private final PowerShellSharepointClient powerShellSharepointClient;

    public SharePointConfigurationService(
            MicrosoftGraphClient graphClient,
            PowerShellSharepointClient powerShellSharepointClient,
            ObjectMapper objectMapper,
            MetricsCollector metrics) {
        super(graphClient, objectMapper, metrics);
        this.powerShellSharepointClient = powerShellSharepointClient;
    }

    /**
     * Exports the complete SharePoint and OneDrive configuration.
     */
    public ConfigurationResult exportConfiguration() {
        Instant startTime = Instant.now();
        metrics.recordExportStart();
        logger.info("Starting SharePoint/OneDrive configuration export at {}", startTime);

        try {
            Map<String, CompletableFuture<?>> futures = new HashMap<>();
            futures.put(CONFIG_KEY_SPO_TENANT, getSPOTenant());
            futures.put("SPO_site", getSPOSite());
            futures.put("OneDrive_PnP_Flag", CompletableFuture.completedFuture(true));

            // Wait for all futures to complete with timeout
            ConfigurationResult result = waitForFutures(futures).thenApply(map -> {
                map.put("SharePoint_successful_commands", getSuccessfulCommands());
                map.put("SharePoint_unsuccessful_commands", getUnsuccessfulCommands());
                return buildConfigurationResult(map, SERVICE_VERSION, ConfigurationServiceType.SHAREPOINT);
            }).get(DEFAULT_TIMEOUT.toMinutes(), TimeUnit.MINUTES);

            Duration duration = Duration.between(startTime, Instant.now());
            metrics.recordExportSuccess(duration);
            logger.info("SharePoint/OneDrive configuration export completed in {} seconds", duration.toSeconds());

            return result;
        } catch (Exception e) {
            metrics.recordExportFailure();
            logger.error("SharePoint/OneDrive configuration export failed", e);
            throw new ConfigurationExportException("SharePoint/OneDrive configuration export failed", e);
        }
    }

    private CompletableFuture<JsonNode> getSPOTenant() {
        CompletableFuture<TenantProperties> tenantPropertiesFuture = getTenantProperties();
        CompletableFuture<AllowToBeDeleted> allowToBeDeletedFuture = getAllowToBeDeleted();

        return tenantPropertiesFuture.thenCombine(allowToBeDeletedFuture, (sharePointTenantProperties, allowToBeDeleted) -> {
                    try {
                        SharePointTenantProperties spoTenant = new SharePointTenantProperties(sharePointTenantProperties, allowToBeDeleted.allowToBeDeletedSPO, allowToBeDeleted.allowToBeDeletedODB);
                        JsonNode resultNode = objectMapper.valueToTree(spoTenant);
                        ArrayNode arrayNode = JsonNodeFactory.instance.arrayNode();
                        arrayNode.add(resultNode);
                        return arrayNode;
                    } catch (Exception ex) {
                        logger.error("Can not make PnPSPOTenant ", ex);
                        throw ex;
                    }
                }
        );
    }

    private CompletableFuture<TenantProperties> getTenantProperties() {
        SPShellCommand<TenantProperties> command = SPShellCommand.PnPTenant.GET_ALL();

        return Retry.executeWithRetry(() -> powerShellSharepointClient.execute(command), MAX_RETRY)
                .thenCompose(tenantProperties -> {
                    if (tenantProperties == null || tenantProperties.isEmpty()) {
                        logger.error("Command {} return empty result", command.getName());
                        return CompletableFuture.failedFuture(new SharePointClientException("Command return empty result"));
                    }

                    if (tenantProperties.size() != 1) {
                        logger.error("Command {} return {} properties. select first properties", command.getName(), tenantProperties.size());
                    }
                    successfulCommands.add(command.getName());
                    return CompletableFuture.completedFuture(tenantProperties.getFirst());
                })
                .exceptionallyCompose(exception -> {
                    unsuccessfulCommands.add(command.getName());
                    return CompletableFuture.failedFuture(exception);
                });
    }

    private CompletableFuture<AllowToBeDeleted> getAllowToBeDeleted() {
        SPShellCommand<AllowToBeDeleted> command = SPShellCommand.PnPTenant.GET_ALL_ALLOW_TO_BE_DELETED();
        return Retry.executeWithRetry(() -> powerShellSharepointClient.execute(command), MAX_RETRY)
                .thenCompose(list -> {
                    if (list == null || list.isEmpty()) {
                        logger.error("Command {} return empty result", command.getName());
                        return CompletableFuture.failedFuture(new SharePointClientException("Command return empty result"));
                    }

                    if (list.size() != 1) {
                        logger.error("Command {} return {} properties. select first properties", command.getName(), list.size());
                    }
                    successfulCommands.add(command.getName());
                    return CompletableFuture.completedFuture(list.getFirst());
                })
                .exceptionallyCompose(exception -> {
                    unsuccessfulCommands.add(command.getName());
                    return CompletableFuture.failedFuture(exception);
                });

    }

    private CompletableFuture<JsonNode> getSPOSite() {
        return withRetry(() -> getSite(graphClient), "Get-SPOSite")
                .thenCompose(site -> {
                    SPShellCommand<SiteProperties> command = SPShellCommand.PnPTenantSite.GET(site.getWebUrl());
                    return Retry.executeWithRetry(() -> powerShellSharepointClient.execute(command), MAX_RETRY)
                            .thenCompose(list -> {
                                if (list == null || list.isEmpty()) {
                                    logger.error("Command {} return empty result", command.getName());
                                    return CompletableFuture.failedFuture(new SharePointClientException("Command return empty result"));
                                }

                                if (list.size() != 1) {
                                    logger.error("Command {} return {} properties. select first properties", command.getName(), list.size());
                                }
                                    SharePointSite sharePointSite = new SharePointSite(list.getFirst());
                                    JsonNode resultNode = objectMapper.valueToTree(sharePointSite);
                                    ArrayNode arrayNode = JsonNodeFactory.instance.arrayNode();
                                    arrayNode.add(resultNode);
                                    return CompletableFuture.completedFuture((JsonNode)arrayNode);
                            });
                }).exceptionallyCompose(exception -> {
                    unsuccessfulCommands.add("Get-PnPTenantSite");
                    return CompletableFuture.failedFuture(exception);
                });
    }

    private static CompletableFuture<Site> getSite(MicrosoftGraphClient graphClient) {
        return graphClient.makeGraphRequest(
                GraphRequest.builder()
                        .v1()
                        .withEndpoint("/sites/root")
                        .build()
                , Site.class);
    }

    public static class SharePointClientException extends SyrixRuntimeException {
        public SharePointClientException(String message) {
            super(message);
        }
    }
}