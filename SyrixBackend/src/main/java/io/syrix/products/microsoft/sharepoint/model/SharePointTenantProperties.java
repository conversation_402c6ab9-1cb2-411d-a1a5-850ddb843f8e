package io.syrix.products.microsoft.sharepoint.model;

import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.syrix.protocols.client.sharepoint.powershell.cscom.command.mstypes.TenantProperties;

import java.util.HashMap;
import java.util.Map;

import static java.util.Objects.requireNonNullElse;

/// / Assembly location: C:\Program Files\WindowsPowerShell\Modules\PnP.PowerShell\2.12.0\Core\PnP.PowerShell.dll
/// namespace PnP.PowerShell.Commands.Model

public class SharePointTenantProperties {
    @JsonProperty("_ObjectIdentity_")
    public String objectIdentity;
    @JsonProperty("_ObjectType_")
    public String objectType;


    public Integer sharingCapability; //SharingCapabilities
    public Integer oneDriveSharingCapability; //SharingCapabilities?
    public Integer sharingDomainRestrictionMode; //SharingDomainRestrictionModes
    public Integer defaultSharingLinkType; //SharingLinkType
    public Integer defaultLinkPermission; //SharingPermissionType
    public int requireAnonymousLinksExpireInDays;
    public Integer fileAnonymousLinkType; //AnonymousLinkType
    public Integer folderAnonymousLinkType; //AnonymousLinkType
    public boolean emailAttestationRequired;
    public int emailAttestationReAuthDays;
    public String sharingAllowedDomainList;
    public String sharingBlockedDomainList;
    public boolean requireAcceptingAccountMatchInvitedAccount;
    public Boolean allowFilesWithKeepLabelToBeDeletedSPO;
    public Boolean allowFilesWithKeepLabelToBeDeletedODB;
	public Boolean disallowInfectedFileDownload;

    private final Map<String, Object> additionalProperties = new HashMap<>();

    @JsonAnyGetter
    public Map<String, Object> getAdditionalProperties() {
        return additionalProperties;
    }

    @JsonAnySetter
    public void setAdditionalProperty(String name, Object value) {
        additionalProperties.put(name, value);
    }

    public SharePointTenantProperties() {}

    public SharePointTenantProperties(TenantProperties tenant, Boolean allowToBeDeletedSPO, Boolean allowToBeDeletedODB) {
        this.objectIdentity = tenant.objectIdentity;
        this.objectType = tenant.objectType;
        this.allowFilesWithKeepLabelToBeDeletedSPO = allowToBeDeletedSPO;
        this.allowFilesWithKeepLabelToBeDeletedODB = allowToBeDeletedODB;

        this.sharingCapability = tenant.sharingCapability;
        this.requireAcceptingAccountMatchInvitedAccount = tenant.requireAcceptingAccountMatchInvitedAccount;
        this.requireAnonymousLinksExpireInDays = requireNonNullElse(tenant.requireAnonymousLinksExpireInDays, 0);
        this.sharingAllowedDomainList = tenant.sharingAllowedDomainList;
        this.sharingBlockedDomainList = tenant.sharingBlockedDomainList;
        this.sharingDomainRestrictionMode = tenant.sharingDomainRestrictionMode;
        this.defaultSharingLinkType = requireNonNullElse(tenant.defaultSharingLinkType, 0); //SharingLinkType.None
        this.fileAnonymousLinkType = requireNonNullElse(tenant.fileAnonymousLinkType, 0); //AnonymousLinkType.None;
        this.folderAnonymousLinkType = requireNonNullElse(tenant.folderAnonymousLinkType, 0); //AnonymousLinkType.None;
        this.defaultLinkPermission = requireNonNullElse(tenant.defaultLinkPermission, 0); //SharingPermissionType.None;
        this.emailAttestationRequired = requireNonNullElse(tenant.emailAttestationRequired, false);
        this.emailAttestationReAuthDays = requireNonNullElse(tenant.emailAttestationReAuthDays, 30);
        this.oneDriveSharingCapability = tenant.odbSharingCapability;
		this.disallowInfectedFileDownload = tenant.disallowInfectedFileDownload;
        
        this.additionalProperties.putAll(tenant.getAdditionalProperties());
    }

}