package io.syrix.products.microsoft.teams.remediation;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.products.microsoft.base.IPolicyRemediator;
import io.syrix.products.microsoft.base.IPolicyRemediatorRollback;
import io.syrix.products.microsoft.base.PolicyRemediator;
import io.syrix.products.microsoft.base.PolicyChangeResult;
import io.syrix.products.microsoft.base.ParameterChangeResult;
import io.syrix.products.microsoft.base.ParameterChangeStatus;
import io.syrix.products.microsoft.base.RemediationResult;
import io.syrix.datamodel.task.remediation.teams.TeamsRemediationConfig;
import io.syrix.products.microsoft.teams.model.TeamsMeetingPolicy;
import io.syrix.protocols.client.PowerShellTeamsClient;
import io.syrix.protocols.client.teams.powershell.command.CsTeamsCommand;
import io.syrix.protocols.client.teams.powershell.command.types.MeetingPolicy;

import java.time.Instant;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.ArrayList;

@PolicyRemediator("MS.TEAMS.1.1v1")
public class TeamsMeetingsAllowingExternalControlRemediator extends TeamsRemediatorBase implements IPolicyRemediatorRollback {

	public TeamsMeetingsAllowingExternalControlRemediator(PowerShellTeamsClient client, ObjectNode configNode, TeamsRemediationConfig remediationConfig) {
		super(client, configNode, remediationConfig);
	}

	public TeamsMeetingsAllowingExternalControlRemediator(PowerShellTeamsClient client) {
		super(client, null, null);
	}

	@Override
	public CompletableFuture<JsonNode> remediate() {
		return remediate_().thenApply(jsonMapper::valueToTree);
	}

	@Override
	public CompletableFuture<PolicyChangeResult> remediate_() {
		List<TeamsMeetingPolicy> policies = getTeamsMeetingPolicies();

		return executeRemediationWorkflow(
				policies,
				policy -> policy.allowExternalParticipantGiveRequestControl && !policy.identity.startsWith("Tag:"),
				this::fixPolicy,
				"All meeting policies secured - external control disabled successfully",
				"Failed to secure any meeting policies",
				"Secured %d policies, failed to secure %d policies"
		);
	}

	private CompletableFuture<PolicyChangeResult> fixPolicy(TeamsMeetingPolicy policy) {
		MeetingPolicy meetingPolicy = new MeetingPolicy();
		meetingPolicy.identity = policy.identity;
		meetingPolicy.allowExternalParticipantGiveRequestControl = false;

		ParameterChangeResult paramChange = new ParameterChangeResult()
				.timeStamp(Instant.now())
				.parameter("allowExternalParticipantGiveRequestControl: " + policy.identity)
				.prevValue(policy.allowExternalParticipantGiveRequestControl)
				.newValue(meetingPolicy.allowExternalParticipantGiveRequestControl);

		return client.execute(CsTeamsCommand.CsTeamsMeetingPolicy.SET(meetingPolicy))
				// the command returns an empty result
				.thenApply(config -> {
					paramChange.status(ParameterChangeStatus.SUCCESS);
					return IPolicyRemediator.success_(getPolicyId(), "Teams Meeting Policy: " + meetingPolicy.identity, List.of(paramChange));
				})
				.exceptionally(ex -> {
					logger.warn("Failed to update Teams Meeting Policy: {} , errMsg: {}", meetingPolicy.identity, ex.getMessage());
					paramChange.status(ParameterChangeStatus.FAILED);
					return IPolicyRemediator.failed_(getPolicyId(), "Failed to update Teams Meeting Policy '" + meetingPolicy.identity + "': " + ex.getMessage(), List.of(paramChange));
				});
	}


	@Override
	public CompletableFuture<PolicyChangeResult> rollback(PolicyChangeResult fixResult) {
		try {
			List<ParameterChangeResult> changes = fixResult.getChanges();
			List<CompletableFuture<PolicyChangeResult>> results = new ArrayList<>();

			for (ParameterChangeResult change : changes) {
				String identity = change.getParameter().split(": ")[1];
				boolean allowExternalParticipantGiveRequestControl = Boolean.parseBoolean(change.getPrevValue().toString());
				boolean prevAllowExternalParticipantGiveRequestControl = Boolean.parseBoolean(change.getNewValue().toString());

				MeetingPolicy meetingPolicy = new MeetingPolicy();
				meetingPolicy.identity = identity;
				meetingPolicy.allowExternalParticipantGiveRequestControl = allowExternalParticipantGiveRequestControl;

				ParameterChangeResult paramChange = new ParameterChangeResult()
						.timeStamp(Instant.now())
						.parameter("allowExternalParticipantGiveRequestControl: " + identity)
						.prevValue(prevAllowExternalParticipantGiveRequestControl)
						.newValue(allowExternalParticipantGiveRequestControl);

				if (change.getStatus() != ParameterChangeStatus.SUCCESS) {
					logger.error("Rollback the policy: {} identity: {} skipped", getPolicyId(), identity);
					paramChange.status(ParameterChangeStatus.FAILED);
					results.add(CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(),
							"Rollback the policy " + getPolicyId() + " identity: " + identity + " skipped", List.of(paramChange))));
					continue;
				}

				CompletableFuture<PolicyChangeResult> result = client.execute(CsTeamsCommand.CsTeamsMeetingPolicy.SET(meetingPolicy))
						.thenApply(jsonNode -> {
							paramChange.status(ParameterChangeStatus.SUCCESS);
							return IPolicyRemediator.success_(getPolicyId(), "Successfully rolled back meetings allowing external control policy", List.of(paramChange));
						})
						.exceptionally(ex -> {
							paramChange.status(ParameterChangeStatus.FAILED);
							logger.error("Exception during meetings allowing external control policy rollback", ex);
							return IPolicyRemediator.failed_(getPolicyId(), ex.getMessage(), List.of(paramChange));
						});

				results.add(result);
			}

			return combineResults(results,
					"All external control policies rolled back successfully",
					"Failed to rollback any external control policies",
					"Rolled back %d policies, failed to rollback %d policies");
		} catch (Exception ex) {
			logger.error("Rollback the policy {} failed", getPolicyId(), ex);
			return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), ex.getMessage()));
		}
	}
}
