package io.syrix.products.microsoft.teams.remediation;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.products.microsoft.base.IPolicyRemediator;
import io.syrix.products.microsoft.base.IPolicyRemediatorRollback;
import io.syrix.products.microsoft.base.PolicyRemediator;
import io.syrix.products.microsoft.base.PolicyChangeResult;
import io.syrix.products.microsoft.base.ParameterChangeResult;
import io.syrix.products.microsoft.base.ParameterChangeStatus;
import io.syrix.products.microsoft.base.RemediationResult;
import io.syrix.datamodel.task.remediation.teams.TeamsRemediationConfig;
import io.syrix.products.microsoft.teams.model.TeamAppPermissionPolicy;
import io.syrix.protocols.client.PowerShellTeamsClient;
import io.syrix.protocols.client.teams.powershell.command.CsTeamsCommand;
import io.syrix.protocols.client.teams.powershell.command.types.AppPermissionPolicy;
import io.syrix.protocols.client.teams.powershell.command.types.CatalogAppsType;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;

@PolicyRemediator("MS.TEAMS.5.2v1")
public class TeamsGlobalCatalogAppsTypeRemediator extends TeamsRemediatorBase implements IPolicyRemediatorRollback {
	public TeamsGlobalCatalogAppsTypeRemediator(PowerShellTeamsClient client, ObjectNode configNode, TeamsRemediationConfig remediationConfig) {
		super(client, configNode, remediationConfig);
	}

	public TeamsGlobalCatalogAppsTypeRemediator(PowerShellTeamsClient client) {
		super(client, null, null);
	}

	@Override
	public CompletableFuture<JsonNode> remediate() {
		return remediate_().thenApply(jsonMapper::valueToTree);
	}

	@Override
	public CompletableFuture<PolicyChangeResult> remediate_() {
		List<TeamAppPermissionPolicy> teamAppPermissionPolicies = getTeamsAppPermissionPolicies();

		return executeRemediationWorkflow(
				teamAppPermissionPolicies,
				policy -> policy.globalCatalogAppsType != null && CatalogAppsType.BLOCKED_APP_LIST.asString().equals(policy.globalCatalogAppsType),
				this::fixConfig,
				"All app permission policies secured - global catalog apps set to allowed list successfully",
				"Failed to secure any app permission policies",
				"Secured %d policies, failed to secure %d policies"
		);
	}

	private CompletableFuture<PolicyChangeResult> fixConfig(TeamAppPermissionPolicy policy) {
		AppPermissionPolicy appPermissionPolicy = new AppPermissionPolicy();
		appPermissionPolicy.identity = policy.identity;
		appPermissionPolicy.globalCatalogAppsType = CatalogAppsType.ALLOWED_APP_LIST;

		ParameterChangeResult paramChange = new ParameterChangeResult()
				.timeStamp(Instant.now())
				.parameter("globalCatalogAppsType: " + policy.identity)
				.prevValue(policy.globalCatalogAppsType)
				.newValue(appPermissionPolicy.globalCatalogAppsType.asString());

		return client.execute(CsTeamsCommand.CsTeamsAppPermissionPolicy.SET(appPermissionPolicy))
				.thenApply(config -> {
					paramChange.status(ParameterChangeStatus.SUCCESS);
					return IPolicyRemediator.success_(getPolicyId(), "Teams App Permission Policy: " + appPermissionPolicy.identity, List.of(paramChange));
				})
				.exceptionally(ex -> {
					logger.warn("Failed to update Teams App Permission Policy: {} , errMsg: {}", appPermissionPolicy.identity, ex.getMessage());
					paramChange.status(ParameterChangeStatus.FAILED);
					return IPolicyRemediator.failed_(getPolicyId(), "Failed to update Teams App Permission Policy '" + appPermissionPolicy.identity + "': " + ex.getMessage(), List.of(paramChange));
				});
	}


	@Override
	public CompletableFuture<PolicyChangeResult> rollback(PolicyChangeResult fixResult) {
		try {
			List<ParameterChangeResult> changes = fixResult.getChanges();
			List<CompletableFuture<PolicyChangeResult>> results = new ArrayList<>();

			for (ParameterChangeResult change : changes) {
				String identity = change.getParameter().split(": ")[1];
				CatalogAppsType globalCatalogAppsType = CatalogAppsType.fromString(change.getPrevValue().toString());
				String prevGlobalCatalogAppsType = change.getNewValue().toString();

				AppPermissionPolicy appPermissionPolicy = new AppPermissionPolicy();
				appPermissionPolicy.identity = identity;
				appPermissionPolicy.globalCatalogAppsType = globalCatalogAppsType;

				ParameterChangeResult paramChange = new ParameterChangeResult()
						.timeStamp(Instant.now())
						.parameter("globalCatalogAppsType: " + identity)
						.prevValue(prevGlobalCatalogAppsType)
						.newValue(globalCatalogAppsType.asString());

				if (change.getStatus() != ParameterChangeStatus.SUCCESS) {
					logger.error("Rollback the policy: {} identity: {} skipped", getPolicyId(), identity);
					paramChange.status(ParameterChangeStatus.FAILED);
					results.add(CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(),
							"Rollback the policy " + getPolicyId() + " identity: " + identity + " skipped", List.of(paramChange))));
					continue;
				}

				CompletableFuture<PolicyChangeResult> result = client.execute(CsTeamsCommand.CsTeamsAppPermissionPolicy.SET(appPermissionPolicy))
						.thenApply(jsonNode -> {
							paramChange.status(ParameterChangeStatus.SUCCESS);
							return IPolicyRemediator.success_(getPolicyId(), "Successfully rolled back global catalog apps type policy", List.of(paramChange));
						})
						.exceptionally(ex -> {
							paramChange.status(ParameterChangeStatus.FAILED);
							logger.error("Exception during global catalog apps type policy rollback", ex);
							return IPolicyRemediator.failed_(getPolicyId(), ex.getMessage(), List.of(paramChange));
						});

				results.add(result);
			}

			return combineResults(results,
					"All global catalog app policies rolled back successfully",
					"Failed to rollback any global catalog app policies",
					"Rolled back %d policies, failed to rollback %d policies");
		} catch (Exception ex) {
			logger.error("Rollback the policy {} failed", getPolicyId(), ex);
			return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), ex.getMessage()));
		}
	}
}
