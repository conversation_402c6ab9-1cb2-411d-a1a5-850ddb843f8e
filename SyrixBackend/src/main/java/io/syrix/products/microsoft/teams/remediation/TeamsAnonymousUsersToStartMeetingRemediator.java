package io.syrix.products.microsoft.teams.remediation;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.products.microsoft.base.IPolicyRemediator;
import io.syrix.products.microsoft.base.IPolicyRemediatorRollback;
import io.syrix.products.microsoft.base.ParameterChangeResult;
import io.syrix.products.microsoft.base.ParameterChangeStatus;
import io.syrix.products.microsoft.base.PolicyChangeResult;
import io.syrix.products.microsoft.base.PolicyRemediator;
import io.syrix.products.microsoft.base.RemediationResult;
import io.syrix.datamodel.task.remediation.teams.TeamsRemediationConfig;
import io.syrix.products.microsoft.teams.model.TeamsMeetingPolicy;
import io.syrix.protocols.client.PowerShellTeamsClient;
import io.syrix.protocols.client.teams.powershell.command.CsTeamsCommand;
import io.syrix.protocols.client.teams.powershell.command.types.MeetingPolicy;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;

@PolicyRemediator("MS.TEAMS.1.2v1")
public class TeamsAnonymousUsersToStartMeetingRemediator extends TeamsRemediatorBase implements IPolicyRemediatorRollback {

	public TeamsAnonymousUsersToStartMeetingRemediator(PowerShellTeamsClient client, ObjectNode configNode, TeamsRemediationConfig remediationConfig) {
		super(client, configNode, remediationConfig);
	}

	public TeamsAnonymousUsersToStartMeetingRemediator(PowerShellTeamsClient client) {
		super(client, null, null);
	}

	@Override
	public CompletableFuture<JsonNode> remediate() {
		return remediate_().thenApply(jsonMapper::valueToTree);
	}

	@Override
	public CompletableFuture<PolicyChangeResult> remediate_() {
		List<TeamsMeetingPolicy> policies = getTeamsMeetingPolicies();

		return executeRemediationWorkflow(
				policies,
				policy -> policy.allowAnonymousUsersToStartMeeting && !policy.identity.startsWith("Tag:"),
				this::fixPolicy_,
				"All meeting policies secured - anonymous meeting start disabled successfully",
				"Failed to secure any meeting policies",
				"Secured %d policies, failed to secure %d policies"
		);
	}

	private CompletableFuture<PolicyChangeResult> fixPolicy_(TeamsMeetingPolicy policy) {
		MeetingPolicy meetingPolicy = new MeetingPolicy();
		meetingPolicy.identity = policy.identity;
		meetingPolicy.allowAnonymousUsersToStartMeeting = false;

		ParameterChangeResult anonymousStartParam = new ParameterChangeResult()
				.timeStamp(Instant.now())
				.parameter("allowAnonymousUsersToStartMeeting: " + policy.identity)
				.prevValue(policy.allowAnonymousUsersToStartMeeting)
				.newValue(meetingPolicy.allowAnonymousUsersToStartMeeting);

		return client.execute(CsTeamsCommand.CsTeamsMeetingPolicy.SET(meetingPolicy))
				.thenApply(meetingPolicies -> {
					anonymousStartParam.status(ParameterChangeStatus.SUCCESS);
					return IPolicyRemediator.success_(getPolicyId(), "Meeting Policy fixed: " + policy.identity, List.of(anonymousStartParam));
				})
				.exceptionally(ex -> {
					logger.warn("Failed to update Meeting Policy: {} , errMsg: {}", policy.identity, ex.getMessage());
					anonymousStartParam.status(ParameterChangeStatus.FAILED);
					return IPolicyRemediator.failed_(getPolicyId(), "Failed to update Meeting Policy: " + ex.getMessage(), List.of(anonymousStartParam));
				});
	}


	@Override
	public CompletableFuture<PolicyChangeResult> rollback(PolicyChangeResult fixResult) {
		try {
			List<ParameterChangeResult> changes = fixResult.getChanges();
			List<CompletableFuture<PolicyChangeResult>> results = new ArrayList<>();

			for (ParameterChangeResult change : changes) {
				String identity = change.getParameter().split(": ")[1];
				boolean allowAnonymousUsersToStartMeeting = Boolean.parseBoolean(change.getPrevValue().toString());
				boolean prevAllowAnonymousUsersToStartMeeting = Boolean.parseBoolean(change.getNewValue().toString());

				MeetingPolicy meetingPolicy = new MeetingPolicy();
				meetingPolicy.identity = identity;
				meetingPolicy.allowAnonymousUsersToStartMeeting = allowAnonymousUsersToStartMeeting;

				ParameterChangeResult anonymousStartParam = new ParameterChangeResult()
						.timeStamp(Instant.now())
						.parameter("allowAnonymousUsersToStartMeeting: " + identity)
						.prevValue(prevAllowAnonymousUsersToStartMeeting)
						.newValue(allowAnonymousUsersToStartMeeting);

				if (change.getStatus() != ParameterChangeStatus.SUCCESS) {
					logger.error("Rollback the policy: {} identity: {} skipped", getPolicyId(), identity);
					anonymousStartParam.status(ParameterChangeStatus.FAILED);
					results.add(CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(),
							"Rollback the policy " + getPolicyId() + " identity: " + identity + " skipped", List.of(anonymousStartParam))));
					continue;
				}

				CompletableFuture<PolicyChangeResult> result = client.execute(CsTeamsCommand.CsTeamsMeetingPolicy.SET(meetingPolicy))
						.thenApply(jsonNode -> {
							anonymousStartParam.status(ParameterChangeStatus.SUCCESS);
							return IPolicyRemediator.success_(getPolicyId(), "Successfully rolled back anonymous users to start meeting policy", List.of(anonymousStartParam));
						})
						.exceptionally(ex -> {
							anonymousStartParam.status(ParameterChangeStatus.FAILED);
							logger.error("Exception during anonymous users to start meeting policy rollback", ex);
							return IPolicyRemediator.failed_(getPolicyId(), ex.getMessage(), List.of(anonymousStartParam));
						});

				results.add(result);
			}

			return combineResults(results,
					"All anonymous meeting start policies rolled back successfully",
					"Failed to rollback any anonymous meeting start policies",
					"Rolled back %d policies, failed to rollback %d policies");
		} catch (Exception ex) {
			logger.error("Rollback the policy {} failed", getPolicyId(), ex);
			return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), ex.getMessage()));
		}
	}
}
