package io.syrix.products.microsoft.teams.remediation;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.products.microsoft.base.IPolicyRemediator;
import io.syrix.products.microsoft.base.IPolicyRemediatorRollback;
import io.syrix.products.microsoft.base.PolicyRemediator;
import io.syrix.products.microsoft.base.PolicyChangeResult;
import io.syrix.products.microsoft.base.ParameterChangeResult;
import io.syrix.products.microsoft.base.ParameterChangeStatus;
import io.syrix.products.microsoft.base.RemediationResult;
import io.syrix.datamodel.task.remediation.teams.TeamsRemediationConfig;
import io.syrix.products.microsoft.teams.model.TeamsMeetingPolicy;
import io.syrix.protocols.client.PowerShellTeamsClient;
import io.syrix.protocols.client.teams.powershell.command.CsTeamsCommand;
import io.syrix.protocols.client.teams.powershell.command.types.MeetingPolicy;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;

@PolicyRemediator("MS.TEAMS.1.5v1")
public class TeamsMeetingsAllowingPSTNBypassRemediator extends TeamsRemediatorBase implements IPolicyRemediatorRollback {

	public TeamsMeetingsAllowingPSTNBypassRemediator(PowerShellTeamsClient client, ObjectNode configNode, TeamsRemediationConfig remediationConfig) {
		super(client, configNode, remediationConfig);
	}

	public TeamsMeetingsAllowingPSTNBypassRemediator(PowerShellTeamsClient client) {
		super(client, null, null);
	}

	@Override
	public CompletableFuture<JsonNode> remediate() {
		return remediate_().thenApply(jsonMapper::valueToTree);
	}

	@Override
	public CompletableFuture<PolicyChangeResult> remediate_() {
		List<TeamsMeetingPolicy> policies = getTeamsMeetingPolicies();

		if (policies.isEmpty()) {
			return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), "No meeting policies found"));
		}

		List<CompletableFuture<PolicyChangeResult>> results = policies.stream()
				.filter(policy -> policy.allowPSTNUsersToBypassLobby)
				.filter(policy -> !policy.identity.startsWith("Tag:"))
				.map(this::fixPolicy)
				.toList();

		if (results.isEmpty()) {
			return CompletableFuture.completedFuture(IPolicyRemediator.requirementMet_(getPolicyId()));
		}

		return combineResults(results);
	}

	private CompletableFuture<PolicyChangeResult> fixPolicy(TeamsMeetingPolicy policy) {
		MeetingPolicy meetingPolicy = new MeetingPolicy();
		meetingPolicy.identity = policy.identity;
		meetingPolicy.allowPSTNUsersToBypassLobby = false;

		ParameterChangeResult paramChange = new ParameterChangeResult()
				.timeStamp(Instant.now())
				.parameter("allowPSTNUsersToBypassLobby: " + policy.identity)
				.prevValue(policy.allowPSTNUsersToBypassLobby)
				.newValue(meetingPolicy.allowPSTNUsersToBypassLobby);

		return client.execute(CsTeamsCommand.CsTeamsMeetingPolicy.SET(meetingPolicy))
				.thenApply(config -> {
					paramChange.status(ParameterChangeStatus.SUCCESS);
					return IPolicyRemediator.success_(getPolicyId(), "Teams Meeting Policy: " + meetingPolicy.identity, List.of(paramChange));
				})
				.exceptionally(ex -> {
					logger.warn("Failed to update Teams Meeting Policy: {} , errMsg: {}", meetingPolicy.identity, ex.getMessage());
					paramChange.status(ParameterChangeStatus.FAILED);
					return IPolicyRemediator.failed_(getPolicyId(), "Failed to update Teams Meeting Policy '" + meetingPolicy.identity + "': " + ex.getMessage(), List.of(paramChange));
				});
	}


	@Override
	public CompletableFuture<PolicyChangeResult> rollback(PolicyChangeResult fixResult) {
		try {
			List<ParameterChangeResult> changes = fixResult.getChanges();
			List<CompletableFuture<PolicyChangeResult>> results = new ArrayList<>();

			for (ParameterChangeResult change : changes) {
				String identity = change.getParameter().split(": ")[1];
				boolean allowPSTNUsersToBypassLobby = Boolean.parseBoolean(change.getPrevValue().toString());
				boolean prevAllowPSTNUsersToBypassLobby = Boolean.parseBoolean(change.getNewValue().toString());

				MeetingPolicy meetingPolicy = new MeetingPolicy();
				meetingPolicy.identity = identity;
				meetingPolicy.allowPSTNUsersToBypassLobby = allowPSTNUsersToBypassLobby;

				ParameterChangeResult paramChange = new ParameterChangeResult()
						.timeStamp(Instant.now())
						.parameter("allowPSTNUsersToBypassLobby: " + identity)
						.prevValue(prevAllowPSTNUsersToBypassLobby)
						.newValue(allowPSTNUsersToBypassLobby);

				if (change.getStatus() != ParameterChangeStatus.SUCCESS) {
					logger.error("Rollback the policy: {} identity: {} skipped", getPolicyId(), identity);
					paramChange.status(ParameterChangeStatus.FAILED);
					results.add(CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), "Rollback the policy " + getPolicyId() + " identity: " + identity + " skipped", List.of(paramChange))));
					continue;
				}

				CompletableFuture<PolicyChangeResult> result = client.execute(CsTeamsCommand.CsTeamsMeetingPolicy.SET(meetingPolicy))
						.thenApply(jsonNode -> {
							paramChange.status(ParameterChangeStatus.SUCCESS);
							return IPolicyRemediator.success_(getPolicyId(), "Successfully rolled back PSTN bypass lobby policy", List.of(paramChange));
						})
						.exceptionally(ex -> {
							paramChange.status(ParameterChangeStatus.FAILED);
							logger.error("Exception during PSTN bypass lobby policy rollback", ex);
							return IPolicyRemediator.failed_(getPolicyId(), ex.getMessage(), List.of(paramChange));
						});

				results.add(result);
			}

			return combineResults(results,
					"All PSTN lobby bypass policies rolled back successfully",
					"Failed to rollback any PSTN lobby bypass policies",
					"Rolled back %d policies, failed to rollback %d policies");
		} catch (Exception ex) {
			logger.error("Rollback the policy {} failed", getPolicyId(), ex);
			return CompletableFuture.completedFuture(IPolicyRemediator.failed_(getPolicyId(), ex.getMessage()));
		}
	}
}
