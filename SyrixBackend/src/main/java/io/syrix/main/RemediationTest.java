package io.syrix.main;

import com.azure.core.credential.TokenCredential;
import com.azure.identity.ClientSecretCredentialBuilder;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.type.CollectionType;
import com.fasterxml.jackson.dataformat.yaml.YAMLMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import io.quarkus.runtime.Quarkus;
import io.syrix.common.metrics.MetricsCollector;
import io.syrix.common.utils.PolicyRemediatorInfo;
import io.syrix.common.utils.PolicyRemediatorRegistry;
import io.syrix.datamodel.task.remediation.exchange.ExchangeRemediationConfig;
import io.syrix.datamodel.task.remediation.sharepoint.SharepointRemediationConfig;
import io.syrix.datamodel.task.remediation.teams.TeamsRemediationConfig;
import io.syrix.products.microsoft.exo.ExchangeOnlineConfigurationService;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.syrix.common.dns.DnsService;
import io.syrix.products.microsoft.exo.remediation.OWAThirdPartyStorageRemediator;
import io.syrix.products.microsoft.exo.remediation.context.ExchangeRemediationContext;
import io.syrix.products.microsoft.sharepoint.SharePointConfigurationService;
import io.syrix.products.microsoft.sharepoint.model.SharePointTenantProperties;
import io.syrix.products.microsoft.sharepoint.remediation.SharePointMalwareProtectionRemediator;
import io.syrix.products.microsoft.teams.TeamsConfigurationService;
import io.syrix.products.microsoft.teams.TeamsRemediationService;
import io.syrix.products.microsoft.teams.remediation.TeamsAnonymousChatRemediator;
import io.syrix.products.microsoft.teams.remediation.TeamsExternalMeetingChatRemediator;
import io.syrix.products.microsoft.teams.remediation.TeamsMeetingPresentationRemediator;
import io.syrix.protocols.client.MicrosoftGraphClient;
import io.syrix.protocols.client.PowerShellClient;
import io.syrix.protocols.client.PowerShellSharepointClient;
import io.syrix.protocols.client.PowerShellTeamsClient;
import io.syrix.protocols.model.MSEnvironment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.file.Paths;
import java.time.Duration;
import java.util.ArrayList;
import java.util.List;


import static io.syrix.products.microsoft.sharepoint.SharepointConstants.CONFIG_KEY_SPO_TENANT;
import static org.slf4j.event.Level.DEBUG;

public class RemediationTest {
	private static final Logger logger = LoggerFactory.getLogger(RemediationTest.class);
	private static final ObjectMapper jsonMapper = new ObjectMapper();
	private static final ObjectMapper yamlMapper = new YAMLMapper();

	public static void main(String[] args) {
		Quarkus.run(args);
		try {
			// Load configuration from YAML file
			if (args.length < 2) {
				logger.error("""
						Specify as arguments:
						args[0] - password for Certificate
						args[1] - path to Certificate""");
				System.exit(1);
			}

			String certPath = args[1];
			String certPass = args[0];
			if (args.length > 3 && "DEBUG".equalsIgnoreCase(args[3])) {
				setLoggingLevel(TeamsRemediationService.class.getName(), DEBUG);
			}
			String configPath = args.length > 2 ? args[2] : "./config.yml";
			Configuration config = yamlMapper.readValue(
					Paths.get(configPath).toFile(),
					Configuration.class
			);

			// Validate required fields
			if (config.credentials() == null ||
					config.credentials().clientId() == null ||
					config.credentials().clientSecret() == null) {
				throw new IllegalArgumentException("Missing required credentials in config file");
			}

			List<String> filteredParams = List.of("io.syrix.protocols.client.MicrosoftGraphClient", "io.syrix.protocols.client.PowerShellTeamsClient");
			List<PolicyRemediatorInfo> remediationInfos = PolicyRemediatorRegistry.getPolicyRemediatorInfos(filteredParams, false);
			MSEnvironment environment = MSEnvironment.valueOf(config.environment());

			jsonMapper.registerModule(new JavaTimeModule());
			// Create metrics collector for all services
			MetricsCollector metrics = new MetricsCollector();

			// Initialize Graph client
			logger.info("Initializing Microsoft Graph client for environment: {}", environment);
			MicrosoftGraphClient graphClient = MicrosoftGraphClient.builder()
					.withClientId(config.credentials().clientId())
					.withClientSecret(config.credentials().clientSecret())
					.withRefreshToken(config.credentials().refreshToken())
					.withEnvironment(environment)
					.withRequestTimeout(Duration.ofMinutes(5))
					.withMaxRetries(3)
					.withAppId(config.credentials().clientId())
					.withCertPath(certPath)
					.withCertPassword(certPass.toCharArray())
					.build();


			TokenCredential credential = new ClientSecretCredentialBuilder()
					.clientId(config.credentials().clientId())
					.clientSecret(config.credentials().clientSecret())
					.tenantId(graphClient.getTenantId())
					.build();

			String domain = graphClient.getDomain().join();
			PowerShellClient powerShellClient = PowerShellClient.builder()
					.withAppId(config.credentials().clientId())
					.withTenantId(graphClient.getTenantId())
					.withCertificatePath(certPath)
					.withCertificatePassword(certPass.toCharArray())
					.withEnvironment(environment)
					.withEndpointPath("/adminapi/beta")  // Use adminapi endpoint
					.withRequestTimeout(Duration.ofMinutes(5))
					.withConnectTimeout(Duration.ofSeconds(30))
					.withMaxRetries(3)
					.withDomain(domain)
					.build();

			// Initialize DNS service and Exchange Online Configuration Service to get config node
			DnsService dnsService = new DnsService.Builder()
					.withAzureCredential(credential)
					.withDnsServers("8.8.4.4", "1.0.0.1")
					.withObjectMapper(jsonMapper)
					.build();
			ExchangeOnlineConfigurationService exchangeService = new ExchangeOnlineConfigurationService(
					graphClient,
					powerShellClient,
					jsonMapper,
					metrics,
					dnsService
			);


			String site = graphClient.getSite();
			String adminDomain = site.replace(".sharepoint.com", "-admin.sharepoint.com").replace("https://","");

			PowerShellSharepointClient powerShellSharepointClient = PowerShellSharepointClient.builder()
					.withAppId(config.credentials().clientId())
					.withDomain(domain)
					.withAdminDomain(adminDomain)
					.withCertificatePath(certPath)
					.withCertificatePassword(certPass.toCharArray())
					.withEnvironment(environment)
					.withConnectTimeout(Duration.ofSeconds(30))
					.withMaxRetries(3)
					.build();

			SharePointConfigurationService shConfigurationService = new SharePointConfigurationService(graphClient, powerShellSharepointClient, jsonMapper, metrics);
			ObjectNode configNode = (ObjectNode) shConfigurationService.exportConfiguration().getData();
//			ObjectNode configNode = (ObjectNode) teamsConfigurationService.exportConfiguration().getData();
//			ObjectNode configNode = (ObjectNode) exchangeService.exportConfiguration().getData();

			// Create remediation context and config
			ExchangeRemediationContext remediationContext = new ExchangeRemediationContext();
			TeamsRemediationConfig teamsRemediationConfig = new TeamsRemediationConfig();
			SharepointRemediationConfig spRemediationConfig = new SharepointRemediationConfig();

			// Extract tenant properties from configNode
			SharePointTenantProperties tenantProperties;
			try {
				tenantProperties = readSharepointTenantProperties(configNode);
				logger.info("Successfully extracted tenant properties. DisallowInfectedFileDownload: {}",
					tenantProperties.disallowInfectedFileDownload);
			} catch (IOException e) {
				logger.error("Failed to extract tenant properties from configNode", e);
				tenantProperties = new SharePointTenantProperties(); // fallback to empty properties
			}

			SharePointMalwareProtectionRemediator remediator = new SharePointMalwareProtectionRemediator(
					powerShellSharepointClient,
					tenantProperties);
			JsonNode result = remediator.remediate().join();

			logger.info("Remediation Result:");
			logger.info(result.toPrettyString());

			// Only validate if remediation was successful
			String resultStatus = result.get("Result").asText();
			if ("SUCCESS".equals(resultStatus)) {
				logger.info("Remediation completed successfully. Starting validation with retry mechanism...");
				waitForPropagationAndValidate(exchangeService);
			} else {
				logger.warn("Remediation failed with status: {}. Skipping validation.", resultStatus);
			}
			Quarkus.asyncExit(0);
		} catch (Exception e) {
			logger.error("Exception occurred", e);
			Quarkus.asyncExit(1);
		}
	}

	/**
	 * Waits for Exchange Online changes to propagate and validates results with retry mechanism.
	 * Uses intelligent retry logic instead of fixed waiting periods.
	 */
	private static void waitForPropagationAndValidate(ExchangeOnlineConfigurationService exchangeService) {
		final int maxRetries = 6; // Maximum number of validation attempts
		final int initialWaitSeconds = 5; // Initial wait before first validation
		final int retryIntervalSeconds = 10; // Wait between retries

		try {
			// Initial wait to allow for immediate propagation
			logger.info("Waiting {} seconds for initial Exchange Online propagation...", initialWaitSeconds);
			Thread.sleep(initialWaitSeconds * 1000);

			for (int attempt = 1; attempt <= maxRetries; attempt++) {
				logger.info("Validation attempt {}/{}", attempt, maxRetries);

				try {
					// Attempt validation
					boolean validationPassed = validateRemediationResults(exchangeService);

					if (validationPassed) {
						logger.info("🎉 Validation passed on attempt {}! Changes have propagated successfully.", attempt);
						return; // Success - exit early
					} else {
						if (attempt < maxRetries) {
							logger.warn("⏳ Validation failed on attempt {}. Changes may still be propagating. Waiting {} seconds before retry...",
									attempt, retryIntervalSeconds);
							Thread.sleep(retryIntervalSeconds * 1000);
						} else {
							logger.error("❌ Validation failed after {} attempts. Changes may take longer to propagate or there may be an issue.", maxRetries);
						}
					}
				} catch (Exception e) {
					logger.error("Exception during validation attempt {}: {}", attempt, e.getMessage());
					if (attempt < maxRetries) {
						logger.info("Retrying validation in {} seconds...", retryIntervalSeconds);
						Thread.sleep(retryIntervalSeconds * 1000);
					}
				}
			}

			logger.warn("Validation completed after {} attempts. Final status may indicate incomplete propagation.", maxRetries);

		} catch (InterruptedException e) {
			logger.error("Validation wait interrupted", e);
			Thread.currentThread().interrupt();
		}
	}

	/**
	 * Validates that the remediation was successful by checking if restricted roles are removed.
	 * Returns true if validation passes, false otherwise.
	 *
	 * @throws RuntimeException if discovery data is missing or invalid
	 */
	private static boolean validateRemediationResults(ExchangeOnlineConfigurationService exchangeService) {
		try {
			// Re-export configuration to get updated state
			ObjectNode updatedConfigNode = (ObjectNode) exchangeService.exportConfiguration().getData();
			JsonNode auditNode = updatedConfigNode.get("role_assignment_addin_audit");

			if (auditNode == null || !auditNode.isObject()) {
				logger.warn("No role assignment audit node found in updated configuration");
				return false; // Return false if no audit node found
			}

			// Extract discovered roles and policies from enhanced structure
			JsonNode discoveredRolesNode = auditNode.get("discovered_roles");
			JsonNode roleAssignmentPolicies = auditNode.get("policies");

			if (roleAssignmentPolicies == null || !roleAssignmentPolicies.isArray()) {
				logger.warn("No role assignment policies found in enhanced configuration structure");
				return false; // Return false if no policies found
			}

			// Get restricted roles from discovered roles - no fallback
			if (discoveredRolesNode == null || !discoveredRolesNode.isObject()) {
				throw new RuntimeException("Discovered roles data is missing or invalid in audit configuration during validation");
			}

			List<String> restrictedRoles = new ArrayList<>();
			discoveredRolesNode.fields().forEachRemaining(entry -> {
				String roleDisplayName = entry.getValue().asText();
				if (roleDisplayName != null && !roleDisplayName.trim().isEmpty()) {
					restrictedRoles.add(roleDisplayName);
				}
			});

			if (restrictedRoles.isEmpty()) {
				throw new RuntimeException("No valid discovered roles found in audit configuration during validation");
			}

			logger.info("Using {} discovered restricted roles for validation: {}", restrictedRoles.size(), restrictedRoles);

			boolean validationPassed = true;
			int totalPolicies = 0;
			int violatingPolicies = 0;

			logger.info("Checking {} role assignment policies for restricted roles...", roleAssignmentPolicies.size());

			// First, log all policies for debugging
			logger.info("=== ALL POLICIES FOUND IN VALIDATION ===");
			for (JsonNode policy : roleAssignmentPolicies) {
				// The 'filterRoleAssignmentPoliciesForRegoValidation' method ensures the key is 'identity'
				String policyName = "Unknown";
				JsonNode identityNode = policy.get("identity");
				if (identityNode != null && !identityNode.isNull()) {
					policyName = identityNode.asText();
				}

				JsonNode roles = policy.get("assignedRoles");
				if (roles != null && roles.isArray()) {
					List<String> rolesList = new ArrayList<>();
					for (JsonNode role : roles) {
						rolesList.add(role.asText());
					}
					logger.info("Policy '{}' has roles: {}", policyName, rolesList);
				} else {
					logger.info("Policy '{}' has no assigned roles", policyName);
				}
			}
			logger.info("=== END POLICY LIST ===");

			for (JsonNode policy : roleAssignmentPolicies) {
				totalPolicies++;

				// Get policy identity - upstream filtering ensures 'identity' field exists
				String policyIdentity = policy.get("identity").asText("Unknown Policy");
				logger.debug("Processing policy: {} (policy structure: {})", policyIdentity, policy.toString());
				JsonNode assignedRoles = policy.get("assignedRoles");

				if (assignedRoles != null && assignedRoles.isArray()) {
					List<String> foundRestrictedRoles = new ArrayList<>();

					for (JsonNode role : assignedRoles) {
						String roleName = role.asText();
						if (restrictedRoles.contains(roleName)) {
							foundRestrictedRoles.add(roleName);
						}
					}

					if (!foundRestrictedRoles.isEmpty()) {
						violatingPolicies++;
						validationPassed = false;
						logger.error("❌ Policy '{}' still contains restricted roles: {}", policyIdentity, foundRestrictedRoles);
					} else {
						logger.info("✅ Policy '{}' is compliant (no restricted roles found)", policyIdentity);
					}
				} else {
					logger.info("✅ Policy '{}' has no assigned roles", policyIdentity);
				}
			}

			// Summary
			logger.info("=== VALIDATION SUMMARY ===");
			logger.info("Total policies checked: {}", totalPolicies);
			logger.info("Policies with violations: {}", violatingPolicies);
			logger.info("Compliant policies: {}", totalPolicies - violatingPolicies);

			if (validationPassed) {
				logger.info("🎉 VALIDATION PASSED: All restricted roles have been successfully removed!");
			} else {
				logger.error("❌ VALIDATION FAILED: {} policies still contain restricted roles", violatingPolicies);
			}

			return validationPassed;

		} catch (Exception e) {
			logger.error("Failed to validate remediation results", e);
			return false; // Return false on exception
		}
	}

	// Helper method to set logging level - Note: In Quarkus, logging levels are configured via application.yml
	private static void setLoggingLevel(String loggerName, org.slf4j.event.Level level) {
		// In Quarkus, logging levels should be configured in application.yml instead of programmatically
		// This method is kept for compatibility but does nothing
		org.slf4j.Logger logger = LoggerFactory.getLogger(loggerName);
		logger.info("Note: Logging level for {} should be configured in application.yml (requested: {})", loggerName, level.name());
	}

	/**
	 * Extracts SharePoint tenant properties from the configuration node
	 */
	private static SharePointTenantProperties readSharepointTenantProperties(JsonNode jsonConfig) throws IOException {
		JsonNode policiesNode = jsonConfig.get(CONFIG_KEY_SPO_TENANT);

		if (policiesNode == null || !policiesNode.isArray()) {
			throw new IOException("Cannot find key '" + CONFIG_KEY_SPO_TENANT + "' in configuration");
		}

		CollectionType collectionType = jsonMapper.getTypeFactory().constructCollectionType(List.class, SharePointTenantProperties.class);
		List<SharePointTenantProperties> tenantProperties = jsonMapper.convertValue(policiesNode, collectionType);

		if (tenantProperties.isEmpty()) {
			throw new IOException("No tenant properties found in configuration");
		}

		return tenantProperties.getFirst();
	}
}
